#!/bin/bash

echo "========================================"
echo "Building TLT Group Management System Installer"
echo "========================================"
echo

# Check if we're in MSYS2 environment
if [[ "$MSYSTEM" == "" ]]; then
    echo "This script should be run from MSYS2 environment"
    echo "Please open MSYS2 terminal and run this script"
    exit 1
fi

# Check for Inno Setup
INNO_SETUP_PATHS=(
    "/c/Program Files (x86)/Inno Setup 6/ISCC.exe"
    "/c/Program Files/Inno Setup 6/ISCC.exe"
    "/mingw64/bin/iscc.exe"
    "/usr/bin/iscc.exe"
)

INNO_SETUP_PATH=""
for path in "${INNO_SETUP_PATHS[@]}"; do
    if [[ -f "$path" ]]; then
        INNO_SETUP_PATH="$path"
        break
    fi
done

if [[ "$INNO_SETUP_PATH" == "" ]]; then
    echo "Inno Setup not found in any of these locations:"
    for path in "${INNO_SETUP_PATHS[@]}"; do
        echo "  - $path"
    done
    echo
    echo "Please install Inno Setup 6 from: https://jrsoftware.org/isinfo.php"
    echo "Or install via MSYS2: pacman -S mingw-w64-x86_64-innosetup"
    exit 1
fi

echo "Found Inno Setup at: $INNO_SETUP_PATH"
echo

# Create output directory
mkdir -p output

# Clean previous builds
rm -f output/*.exe

echo "Compiling installer..."
echo

# Convert Windows path for MSYS2
SETUP_FILE="$(cygpath -w "$(pwd)/setup.iss")"

# Compile the installer
"$INNO_SETUP_PATH" "$SETUP_FILE"

if [[ $? -eq 0 ]]; then
    echo
    echo "========================================"
    echo "Installer built successfully!"
    echo "========================================"
    echo
    echo "Output file: output/TLT_Group_Management_System_Setup.exe"
    echo
    echo "You can now distribute this installer to install the"
    echo "TLT Group Management System on other computers."
    echo
    
    # Open output folder in Windows Explorer
    explorer.exe "$(cygpath -w "$(pwd)/output")"
else
    echo
    echo "========================================"
    echo "Build failed!"
    echo "========================================"
    echo
    echo "Please check the error messages above and fix any issues."
    echo
fi

read -p "Press Enter to continue..."
