[Setup]
AppName=TLT Group Management System
AppVersion=1.0.0
AppPublisher=TLT Group SARL
AppPublisherURL=https://tltgroup.com
AppSupportURL=https://tltgroup.com/support
AppUpdatesURL=https://tltgroup.com/updates
DefaultDirName={autopf}\TLT Group\Management System
DefaultGroupName=TLT Group Management System
AllowNoIcons=yes
LicenseFile=license.txt
InfoBeforeFile=readme.txt
OutputDir=output
OutputBaseFilename=TLT_Group_Management_System_Setup
SetupIconFile=..\resources\TLT icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"
Name: "french"; MessagesFile: "compiler:Languages\French.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1

[Files]
; Main application files
Source: "..\main.py"; DestDir: "{app}"; Flags: ignoreversion
Source: "..\requirements.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "..\README.md"; DestDir: "{app}"; Flags: ignoreversion

; Python modules
Source: "..\modules\*"; DestDir: "{app}\modules"; Flags: ignoreversion recursesubdirs createallsubdirs

; Resources
Source: "..\resources\*"; DestDir: "{app}\resources"; Flags: ignoreversion recursesubdirs createallsubdirs

; Configuration files
Source: "..\config\*"; DestDir: "{app}\config"; Flags: ignoreversion recursesubdirs createallsubdirs

; Logs directory (create empty)
Source: "..\logs\.gitkeep"; DestDir: "{app}\logs"; Flags: ignoreversion

; Python executable and libraries (if bundled)
Source: "python\*"; DestDir: "{app}\python"; Flags: ignoreversion recursesubdirs createallsubdirs; Check: not IsPythonInstalled

; Batch file to run the application
Source: "run_app.bat"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\TLT Group Management System"; Filename: "{app}\run_app.bat"; IconFilename: "{app}\resources\TLT icon.ico"; WorkingDir: "{app}"
Name: "{group}\{cm:UninstallProgram,TLT Group Management System}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\TLT Group Management System"; Filename: "{app}\run_app.bat"; IconFilename: "{app}\resources\TLT icon.ico"; WorkingDir: "{app}"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\TLT Group Management System"; Filename: "{app}\run_app.bat"; IconFilename: "{app}\resources\TLT icon.ico"; WorkingDir: "{app}"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\run_app.bat"; Description: "{cm:LaunchProgram,TLT Group Management System}"; Flags: nowait postinstall skipifsilent

[Code]
function IsPythonInstalled: Boolean;
var
  ResultCode: Integer;
begin
  Result := Exec('python', '--version', '', SW_HIDE, ewWaitUntilTerminated, ResultCode) and (ResultCode = 0);
end;

function InitializeSetup(): Boolean;
begin
  Result := True;
  if not IsPythonInstalled then
  begin
    if MsgBox('Python is not installed on your system. The installer will include a portable Python runtime. Do you want to continue?', 
              mbConfirmation, MB_YESNO) = IDNO then
      Result := False;
  end;
end;

procedure CurStepChanged(CurStep: TSetupStep);
var
  ResultCode: Integer;
begin
  if CurStep = ssPostInstall then
  begin
    // Install Python dependencies if Python is available
    if IsPythonInstalled then
    begin
      Exec('python', '-m pip install -r "' + ExpandConstant('{app}') + '\requirements.txt"', '', SW_SHOW, ewWaitUntilTerminated, ResultCode);
    end;
  end;
end;
