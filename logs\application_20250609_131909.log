2025-06-09 13:19:09,831 - root - INFO - ===== TLT Group Management System Starting =====
2025-06-09 13:19:09,831 - root - INFO - Log file created at: C:\Users\<USER>\Desktop\music project\Accounting101\logs\application_20250609_131909.log
2025-06-09 13:19:09,879 - root - INFO - Searching for icon in: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-09 13:19:09,962 - root - INFO - Set icon using ICO file: C:\Users\<USER>\Desktop\music project\Accounting101\resources\TLT icon.ico
2025-06-09 13:19:09,983 - root - INFO - Found existing database at: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-09 13:19:09,984 - root - INFO - Selected database path: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-09 13:19:09,984 - root - INFO - Ensured database directory exists: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-09 13:19:09,992 - root - INFO - Database initialized successfully with WAL journal mode
2025-06-09 13:19:09,994 - root - INFO - Database check completed successfully
2025-06-09 13:19:09,995 - root - INFO - Loading module: Sales
2025-06-09 13:19:10,081 - root - INFO - Successfully loaded module: Sales
2025-06-09 13:19:10,082 - root - INFO - Loading module: Customers
2025-06-09 13:19:10,151 - root - INFO - Successfully loaded module: Customers
2025-06-09 13:19:10,151 - root - INFO - Loading module: Suppliers
2025-06-09 13:19:10,158 - modules.utils.table_customizer - INFO - Applied column order for suppliers: ['name', 'contact', 'address', 'balance']
2025-06-09 13:19:10,159 - modules.utils.table_customizer - INFO - Loaded saved column order for suppliers
2025-06-09 13:19:10,174 - root - INFO - Successfully loaded module: Suppliers
2025-06-09 13:19:10,174 - root - INFO - Loading module: Purchases
2025-06-09 13:19:10,215 - root - INFO - Successfully loaded module: Purchases
2025-06-09 13:19:10,215 - root - INFO - Loading module: Inventory
2025-06-09 13:19:10,256 - modules.utils.barcode_generator_new - INFO - Successfully imported PIL modules
2025-06-09 13:19:10,289 - modules.utils.barcode_generator_new - INFO - Successfully imported and tested barcode module
2025-06-09 13:19:10,428 - modules.utils.label_generator - INFO - Successfully imported reportlab modules
2025-06-09 13:19:10,475 - modules.utils.table_customizer - INFO - Applied column order for inventory: ['barcode', 'name', 'quantity', 'price', 'cost price', 'category', 'min stock', 'description']
2025-06-09 13:19:10,476 - modules.utils.table_customizer - INFO - Loaded saved column order for inventory
2025-06-09 13:19:10,478 - root - INFO - Successfully loaded module: Inventory
2025-06-09 13:19:10,479 - root - INFO - Loading module: Reports
2025-06-09 13:19:10,514 - root - INFO - Successfully loaded module: Reports
2025-06-09 13:19:10,514 - root - INFO - Loading module: Settings
2025-06-09 13:19:10,609 - root - INFO - Successfully loaded module: Settings
2025-06-09 13:19:10,609 - root - INFO - Application starting in unlocked state
2025-06-09 13:19:10,610 - root - INFO - Initializing security timer
2025-06-09 13:19:10,610 - root - INFO - Loaded security delay from config: 0 seconds
2025-06-09 13:19:10,610 - root - INFO - SecurityTimer initialized with delay: 0 seconds
2025-06-09 13:19:10,612 - root - INFO - Security timer initialized successfully
2025-06-09 13:19:10,613 - root - INFO - Changed to tab: Sales (index: 0)
