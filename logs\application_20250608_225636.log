2025-06-08 22:56:36,875 - root - INFO - ===== TLT Group Management System Starting =====
2025-06-08 22:56:36,876 - root - INFO - Log file created at: C:\Users\<USER>\Desktop\music project\Accounting101\logs\application_20250608_225636.log
2025-06-08 22:56:36,926 - root - INFO - Searching for icon in: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-08 22:56:36,995 - root - INFO - Set icon using ICO file: C:\Users\<USER>\Desktop\music project\Accounting101\resources\TLT icon.ico
2025-06-08 22:56:37,037 - root - INFO - Found existing database at: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-08 22:56:37,038 - root - INFO - Selected database path: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-08 22:56:37,038 - root - INFO - Ensured database directory exists: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-08 22:56:37,041 - root - INFO - Database initialized successfully with WAL journal mode
2025-06-08 22:56:37,044 - root - INFO - Database check completed successfully
2025-06-08 22:56:37,046 - root - INFO - Loading module: Sales
2025-06-08 22:56:37,162 - root - INFO - Successfully loaded module: Sales
2025-06-08 22:56:37,162 - root - INFO - Loading module: Customers
2025-06-08 22:56:37,243 - root - INFO - Successfully loaded module: Customers
2025-06-08 22:56:37,243 - root - INFO - Loading module: Suppliers
2025-06-08 22:56:37,254 - modules.utils.table_customizer - INFO - Applied column order for suppliers: ['name', 'contact', 'address', 'balance']
2025-06-08 22:56:37,254 - modules.utils.table_customizer - INFO - Loaded saved column order for suppliers
2025-06-08 22:56:37,276 - root - INFO - Successfully loaded module: Suppliers
2025-06-08 22:56:37,276 - root - INFO - Loading module: Purchases
2025-06-08 22:56:37,335 - root - INFO - Successfully loaded module: Purchases
2025-06-08 22:56:37,335 - root - INFO - Loading module: Inventory
2025-06-08 22:56:37,386 - modules.utils.barcode_generator_new - INFO - Successfully imported PIL modules
2025-06-08 22:56:37,428 - modules.utils.barcode_generator_new - INFO - Successfully imported and tested barcode module
2025-06-08 22:56:37,583 - modules.utils.label_generator - INFO - Successfully imported reportlab modules
2025-06-08 22:56:37,633 - modules.utils.table_customizer - INFO - Applied column order for inventory: ['barcode', 'name', 'quantity', 'price', 'cost price', 'category', 'min stock', 'description']
2025-06-08 22:56:37,633 - modules.utils.table_customizer - INFO - Loaded saved column order for inventory
2025-06-08 22:56:37,636 - root - INFO - Successfully loaded module: Inventory
2025-06-08 22:56:37,636 - root - INFO - Loading module: Reports
2025-06-08 22:56:37,669 - root - INFO - Successfully loaded module: Reports
2025-06-08 22:56:37,669 - root - INFO - Loading module: Settings
2025-06-08 22:56:37,760 - root - INFO - Successfully loaded module: Settings
2025-06-08 22:56:37,760 - root - INFO - Application starting in unlocked state
2025-06-08 22:56:37,761 - root - INFO - Initializing security timer
2025-06-08 22:56:37,761 - root - INFO - Loaded security delay from config: 0 seconds
2025-06-08 22:56:37,761 - root - INFO - SecurityTimer initialized with delay: 0 seconds
2025-06-08 22:56:37,761 - root - INFO - Security timer initialized successfully
2025-06-08 22:56:37,762 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-08 22:56:43,414 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-08 22:56:43,415 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
2025-06-08 22:57:57,631 - root - INFO - Application closing - performing cleanup
2025-06-08 22:57:57,683 - root - INFO - ===== TLT Group Management System Shutting Down =====
