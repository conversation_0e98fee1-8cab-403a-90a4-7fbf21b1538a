2025-06-09 13:07:30,328 - root - INFO - ===== TLT Group Management System Starting =====
2025-06-09 13:07:30,328 - root - INFO - Log file created at: C:\Users\<USER>\Desktop\music project\Accounting101\logs\application_20250609_130730.log
2025-06-09 13:07:30,576 - root - INFO - Searching for icon in: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-09 13:07:30,644 - root - INFO - Set icon using ICO file: C:\Users\<USER>\Desktop\music project\Accounting101\resources\TLT icon.ico
2025-06-09 13:07:30,699 - root - INFO - Found existing database at: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-09 13:07:30,702 - root - INFO - Selected database path: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-09 13:07:30,704 - root - INFO - Ensured database directory exists: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-09 13:07:30,708 - root - INFO - Database initialized successfully with WAL journal mode
2025-06-09 13:07:30,711 - root - INFO - Database check completed successfully
2025-06-09 13:07:30,713 - root - INFO - Loading module: Sales
2025-06-09 13:07:30,820 - root - INFO - Successfully loaded module: Sales
2025-06-09 13:07:30,821 - root - INFO - Loading module: Customers
2025-06-09 13:07:30,920 - root - INFO - Successfully loaded module: Customers
2025-06-09 13:07:30,920 - root - INFO - Loading module: Suppliers
2025-06-09 13:07:30,930 - modules.utils.table_customizer - INFO - Applied column order for suppliers: ['name', 'contact', 'address', 'balance']
2025-06-09 13:07:30,930 - modules.utils.table_customizer - INFO - Loaded saved column order for suppliers
2025-06-09 13:07:30,946 - root - INFO - Successfully loaded module: Suppliers
2025-06-09 13:07:30,946 - root - INFO - Loading module: Purchases
2025-06-09 13:07:31,014 - root - INFO - Successfully loaded module: Purchases
2025-06-09 13:07:31,014 - root - INFO - Loading module: Inventory
2025-06-09 13:07:31,097 - modules.utils.barcode_generator_new - INFO - Successfully imported PIL modules
2025-06-09 13:07:31,164 - modules.utils.barcode_generator_new - INFO - Successfully imported and tested barcode module
2025-06-09 13:07:31,325 - modules.utils.label_generator - INFO - Successfully imported reportlab modules
2025-06-09 13:07:31,387 - modules.utils.table_customizer - INFO - Applied column order for inventory: ['barcode', 'name', 'quantity', 'price', 'cost price', 'category', 'min stock', 'description']
2025-06-09 13:07:31,388 - modules.utils.table_customizer - INFO - Loaded saved column order for inventory
2025-06-09 13:07:31,391 - root - INFO - Successfully loaded module: Inventory
2025-06-09 13:07:31,391 - root - INFO - Loading module: Reports
2025-06-09 13:07:31,434 - root - INFO - Successfully loaded module: Reports
2025-06-09 13:07:31,434 - root - INFO - Loading module: Settings
2025-06-09 13:07:31,527 - root - INFO - Successfully loaded module: Settings
2025-06-09 13:07:31,528 - root - INFO - Application starting in unlocked state
2025-06-09 13:07:31,528 - root - INFO - Initializing security timer
2025-06-09 13:07:31,528 - root - INFO - Loaded security delay from config: 0 seconds
2025-06-09 13:07:31,529 - root - INFO - SecurityTimer initialized with delay: 0 seconds
2025-06-09 13:07:31,529 - root - INFO - Security timer initialized successfully
2025-06-09 13:07:31,529 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-09 13:07:33,576 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-09 13:07:33,576 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
