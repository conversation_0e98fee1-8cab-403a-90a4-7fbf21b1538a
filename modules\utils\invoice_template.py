"""
T.L.T. GROUP SARL invoice template module
This module provides a customized invoice template for the accounting application
"""

from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image, KeepTogether
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import mm
from reportlab.lib.enums import TA_LEFT
from reportlab.pdfgen import canvas
import os

class InvoiceTemplate(SimpleDocTemplate):
    def __init__(self, filename, **kwargs):
        super().__init__(filename, pagesize=A4, **kwargs)
        self.background_image = None
        self.title_text = "Invoice"
        self.title_position = {'x': 100, 'y': 780}  # Default position
        self.title_color = colors.black
        self.title_font = "Helvetica-Bold"
        self.title_size = 16
        self.status_text = None
        self.status_color = colors.black
        self.is_price_list = False

    def set_background(self, image_path):
        self.background_image = image_path

    def set_title_position(self, x, y):
        self.title_position = {'x': x, 'y': y}

    def set_title(self, text, font=None, size=None, color=None):
        self.title_text = text
        if font:
            self.title_font = font
        if size:
            self.title_size = size
        if color:
            self.title_color = color

    def set_price_list_mode(self, is_price_list=True):
        self.is_price_list = is_price_list

    def set_status(self, text, color=colors.black):
        self.status_text = text
        self.status_color = color

    def build(self, flowables, **kwargs):
        if self.background_image and os.path.exists(self.background_image):
            if self.is_price_list:
                first_page_canvas = self._create_price_list_canvas
            else:
                first_page_canvas = self._create_background_canvas
        else:
            first_page_canvas = self._doNothing

        # Use background only on first page, blank for later pages
        super().build(flowables, onFirstPage=first_page_canvas, onLaterPages=self._doNothing)

    def _create_background_canvas(self, canvas, doc):
        canvas.saveState()

        # Draw background image if available
        if self.background_image and os.path.exists(self.background_image):
            img = Image(self.background_image, self.pagesize[0], self.pagesize[1])
            img.drawOn(canvas, 0, 0)

        # Title is now removed to avoid showing 'INVOICE' at the top
        # We'll keep the code commented in case it needs to be restored later
        # canvas.setFont(self.title_font, self.title_size)
        # canvas.setFillColor(self.title_color)
        # canvas.drawString(self.title_position['x'], self.title_position['y'], self.title_text)

        # Status boxes are removed as requested by the client
        # The status will only be shown in the total row of the invoice

        canvas.restoreState()

    def _create_price_list_canvas(self, canvas, doc):
        canvas.saveState()

        # Draw background image if available
        if self.background_image and os.path.exists(self.background_image):
            img = Image(self.background_image, self.pagesize[0], self.pagesize[1])
            img.drawOn(canvas, 0, 0)

        # Add a note about prices - using Helvetica-Oblique instead of Helvetica-Italic
        canvas.setFont("Helvetica-Oblique", 10)
        canvas.setFillColor(colors.black)
        canvas.drawString(200, 700, "All prices are subject to change without notice")

        canvas.restoreState()

    def _doNothing(self, canvas, doc):
        pass

def generate_custom_invoice(data, filename, options=None):
    """
    Generate a T.L.T. GROUP SARL invoice PDF with background template

    Args:
        data: Dictionary containing invoice data
        filename: Output PDF filename
        options: Dictionary of options (currency, vat_rate, etc.)
            - title_position: Dictionary with 'x' and 'y' coordinates for title placement
            - currency: Currency symbol to use
            - vat_rate: VAT rate percentage
            - status: Invoice status (Paid/Not Paid)
            - is_price_list: Boolean indicating if this is a price list (default: False)
            - title: Custom title to use instead of "INVOICE" (default: None)
    """
    # Default options
    if options is None:
        options = {}

    # Default title position if not provided
    if 'title_position' not in options:
        options['title_position'] = {'x': 100, 'y': 780}  # Default position

    # Default currency if not provided
    currency = options.get('currency', '$')

    # Check if the directory exists and create it if needed
    invoice_dir = os.path.dirname(filename)
    os.makedirs(invoice_dir, exist_ok=True)

    # Check if this is a price list
    is_price_list = options.get('is_price_list', False)

    # Create document with custom template
    doc = InvoiceTemplate(
        filename,
        rightMargin=15*mm,
        leftMargin=15*mm,
        topMargin=15*mm,  # Reduced from default
        bottomMargin=15*mm
    )

    # Get template path and set background
    template_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
        'resources',
        'invoice_template.jpg'
    )

    if os.path.exists(template_path):
        doc.set_background(template_path)
    else:
        print(f"Warning: Invoice template not found at {template_path}")

    # Set custom title if provided
    if 'title' in options:
        doc.set_title(options['title'])

    # Set price list mode if this is a price list
    if options.get('is_price_list', False):
        doc.set_price_list_mode(True)

    # Set custom title position if provided
    if 'title_position' in options:
        doc.set_title_position(options['title_position']['x'], options['title_position']['y'])

    # Set document title
    doc.set_title("INVOICE")

    # Set status if provided
    if 'status' in options:
        status = options['status']
        if status == 'Not Paid':
            doc.set_status("NOT PAID", colors.red)

    # Styles
    styles = getSampleStyleSheet()
    styles.add(ParagraphStyle(
        name='InvoiceLabel',
        parent=styles['Normal'],
        fontSize=10,
        alignment=TA_LEFT,
        spaceAfter=0,
        fontName='Helvetica-Bold'
    ))
    styles.add(ParagraphStyle(
        name='InvoiceValue',
        parent=styles['Normal'],
        fontSize=10,
        alignment=TA_LEFT,
        spaceAfter=0,
        leftIndent=20
    ))

    # Add italic style using Helvetica-Oblique (which is available) instead of Helvetica-Italic
    styles.add(ParagraphStyle(
        name='ItalicText',
        parent=styles['Normal'],
        fontSize=9,
        alignment=TA_LEFT,
        spaceAfter=0,
        fontName='Helvetica-Oblique'
    ))

    elements = []

    # Add space at the top for the letterhead
    elements.append(Spacer(1, 50*mm))

    # Invoice header (modified for price list mode)
    if is_price_list:
        # For price lists, show "Price List" instead of invoice number
        invoice_header = [
            [Paragraph("Price List", styles['InvoiceLabel']),
             Paragraph("", styles['InvoiceValue'])],
            [Paragraph("Date:", styles['InvoiceLabel']),
             Paragraph(data['date'], styles['InvoiceValue'])],
        ]
    else:
        # For regular invoices, show invoice number and date
        invoice_header = [
            [Paragraph("Invoice #:", styles['InvoiceLabel']),
             Paragraph(str(data['invoice_number']), styles['InvoiceValue'])],
            [Paragraph("Date:", styles['InvoiceLabel']),
             Paragraph(data['date'], styles['InvoiceValue'])],
        ]

    # Customer info
    customer_info = [
        [Paragraph("Customer:", styles['InvoiceLabel']),
         Paragraph(data['customer_name'], styles['InvoiceValue'])],
        [Paragraph("Contact:", styles['InvoiceLabel']),
         Paragraph(data['customer_contact'], styles['InvoiceValue'])],
        [Paragraph("Address:", styles['InvoiceLabel']),
         Paragraph(data['customer_address'], styles['InvoiceValue'])],
    ]

    # Create tables for invoice header and customer info
    header_table = Table(invoice_header, colWidths=[30*mm, 60*mm])
    customer_table = Table(customer_info, colWidths=[30*mm, 60*mm])

    # Style for both tables
    table_style = TableStyle([
        ('ALIGN', (0, 0), (0, -1), 'LEFT'),
        ('ALIGN', (1, 0), (1, -1), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 2),
        ('TOPPADDING', (0, 0), (-1, -1), 2),
    ])

    header_table.setStyle(table_style)
    customer_table.setStyle(table_style)

    # Combine header and customer tables into a single row
    combined_table = Table([[header_table, customer_table]], colWidths=[90*mm, 90*mm])
    combined_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (0, 0), 'LEFT'),
        ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
    ]))

    elements.append(combined_table)
    elements.append(Spacer(1, 10*mm))

    # Items table - different headers for price list vs invoice
    if is_price_list:
        # For price lists, don't include the Total column
        items_data = [
            ["#", "Item", "Description", "Qty", "Price"]
        ]

        # Add items without total column
        for i, item in enumerate(data['items'], 1):
            # Handle long descriptions by using Paragraph for text wrapping
            description = item.get('description', '')
            if len(description) > 50:  # If description is long, use Paragraph for wrapping
                description_paragraph = Paragraph(description, ParagraphStyle(
                    'DescriptionStyle',
                    fontSize=8,
                    leading=10,
                    alignment=TA_LEFT,
                    wordWrap='CJK'
                ))
            else:
                description_paragraph = description

            items_data.append([
                str(i),
                item['name'],
                description_paragraph,
                str(item['quantity']),
                f"{currency}{item['price']:.2f}"
            ])
    else:
        # Regular invoice with Total column
        items_data = [
            ["#", "Item", "Description", "Qty", "Price", "Total"]
        ]

        # Add items with total column
        for i, item in enumerate(data['items'], 1):
            # Handle long descriptions by using Paragraph for text wrapping
            description = item.get('description', '')
            if len(description) > 50:  # If description is long, use Paragraph for wrapping
                description_paragraph = Paragraph(description, ParagraphStyle(
                    'DescriptionStyle',
                    fontSize=8,
                    leading=10,
                    alignment=TA_LEFT,
                    wordWrap='CJK'
                ))
            else:
                description_paragraph = description

            items_data.append([
                str(i),
                item['name'],
                description_paragraph,
                str(item['quantity']),
                f"{currency}{item['price']:.2f}",
                f"{currency}{item['total']:.2f}"
            ])

    # For price lists, don't show totals, discounts, VAT, etc.
    if is_price_list:
        # No additional rows for price lists
        pass
    else:
        # Add subtotal row
        items_data.append(['', '', '', '', 'Subtotal:', f"{currency}{data['subtotal']:.2f}"])

        # Add discount row if applicable
        if data.get('discount_amount', 0) > 0:
            discount_text = f"Discount ({data['discount_value']}{'%' if data['discount_type'] == 'percent' else ''}):"
            items_data.append(['', '', '', '', discount_text, f"-{currency}{data['discount_amount']:.2f}"])
            items_data.append(['', '', '', '', 'After Discount:', f"{currency}{data['discounted_subtotal']:.2f}"])

        # Add VAT row if applicable
        if data.get('vat_rate', 0) > 0:
            items_data.append(['', '', '', '', f"VAT ({data['vat_rate']}%):", f"{currency}{data['vat_amount']:.2f}"])

        # Add total row with bold styling and status if applicable
        if options.get('status') == 'Not Paid':
            # Add status to the total row
            items_data.append(['', '', '', '', 'Total $ (NOT PAID):', f"{currency}{data['total']:.2f}"])
        elif options.get('status') == 'Price List':
            # No total for price lists
            pass
        else:
            items_data.append(['', '', '', '', 'Total $:', f"{currency}{data['total']:.2f}"])

        # Add deposit row if applicable
        if data.get('deposit_amount', 0) > 0:
            deposit_date = data.get('deposit_date')
            if deposit_date:
                items_data.append(['', '', '', '', f'Deposit Paid ({deposit_date}):', f"{currency}{data['deposit_amount']:.2f}"])
            else:
                items_data.append(['', '', '', '', 'Deposit Paid:', f"{currency}{data['deposit_amount']:.2f}"])
            items_data.append(['', '', '', '', 'Balance Due:', f"{currency}{data['balance_due']:.2f}"])

    # Calculate column widths - different for price list vs invoice
    if is_price_list:
        # For price lists (no Total column)
        col_widths = [10*mm, 40*mm, 75*mm, 15*mm, 45*mm]  # Wider columns since we removed Total
    else:
        # For regular invoices (with Total column)
        col_widths = [10*mm, 35*mm, 65*mm, 15*mm, 30*mm, 30*mm]

    # Create the items table with automatic row height adjustment
    items_table = Table(items_data, colWidths=col_widths, repeatRows=1)

    # Style for items table
    items_style = [
        # Header style
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),

        # Data style
        ('ALIGN', (0, 1), (0, -1), 'CENTER'),  # Item number centered
        ('ALIGN', (1, 1), (1, -1), 'LEFT'),     # Item name left aligned
        ('ALIGN', (2, 1), (2, -1), 'LEFT'),     # Description left aligned
        ('ALIGN', (3, 1), (3, -1), 'CENTER'),   # Quantity centered
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),    # Vertical alignment to top for all cells
    ]

    # Add price alignment based on whether this is a price list or invoice
    if is_price_list:
        # For price lists (only one price column)
        items_style.append(('ALIGN', (4, 1), (4, -1), 'RIGHT'))  # Price column right aligned

        # Add grid for price lists
        items_style.append(('GRID', (0, 0), (-1, len(data['items'])), 0.5, colors.black))
    else:
        # For regular invoices (price and total columns)
        items_style.append(('ALIGN', (4, 1), (5, -1), 'RIGHT'))  # Price and total columns right aligned

        # Add grid for regular invoices
        items_style.append(('GRID', (0, 0), (-1, len(data['items'])), 0.5, colors.black))

        # Add styling for subtotal and total rows
        items_style.append(('SPAN', (0, -1), (3, -1)))  # Span the empty cells in the total row
        items_style.append(('FONTNAME', (4, -1), (5, -1), 'Helvetica-Bold'))  # Bold font for total label and amount

    # Add special styling for the total row if it's not paid
    if options.get('status') == 'Not Paid':
        items_style.append(('TEXTCOLOR', (4, -1), (5, -1), colors.red))  # Red text for Not Paid
    else:
        items_style.append(('TEXTCOLOR', (4, -1), (5, -1), colors.black))  # Black text for Paid

    # Apply styles to items table
    items_table.setStyle(TableStyle(items_style))

    # Add the items table to the document
    elements.append(items_table)

    # Add some space at the bottom
    elements.append(Spacer(1, 10*mm))

    # Build the document
    doc.build(elements)

    print(f"Invoice generated successfully: {filename}")
    return filename


def generate_account_statement(customer_info, transactions, invoice_details, filename, options=None):
    """
    Generate a T.L.T. GROUP SARL account statement PDF with background template

    Args:
        customer_info: Tuple containing customer information
        transactions: List of transactions
        invoice_details: Dictionary of invoice details
        filename: Output PDF filename
        options: Dictionary of options (currency, etc.)
            - title_position: Dictionary with 'x' and 'y' coordinates for title placement
            - currency: Currency symbol to use
            - optimize_pages: Boolean to optimize content to fit on fewer pages (default: True)
    """
    # Default options
    if options is None:
        options = {}

    # Default title position if not provided
    if 'title_position' not in options:
        options['title_position'] = {'x': 100, 'y': 780}  # Default position

    # Default to optimizing pages
    if 'optimize_pages' not in options:
        options['optimize_pages'] = True

    # Check if the directory exists and create it if needed
    invoice_dir = os.path.dirname(filename)
    os.makedirs(invoice_dir, exist_ok=True)

    # Create document with custom template
    doc = InvoiceTemplate(
        filename,
        rightMargin=10*mm,  # Reduced margin
        leftMargin=10*mm,   # Reduced margin
        topMargin=10*mm,    # Reduced margin
        bottomMargin=10*mm  # Reduced margin
    )

    # Get template path and set background
    template_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
        'resources',
        'invoice_template.jpg'
    )

    if os.path.exists(template_path):
        doc.set_background(template_path)
    else:
        print(f"Warning: Invoice template not found at {template_path}")

    # Set custom title position if provided
    if 'title_position' in options:
        doc.set_title_position(options['title_position']['x'], options['title_position']['y'])

    # Set document title
    doc.set_title("ACCOUNT STATEMENT")

    # Determine if we need to optimize for a single page
    optimize_for_single_page = options.get('optimize_pages', True)

    # Calculate approximate content size to determine if we need to compress
    transaction_count = len(transactions)
    invoice_item_count = sum(len(items) for items in invoice_details.values() if items)
    total_content_items = transaction_count + invoice_item_count

    # Adjust font sizes and spacing based on content amount
    base_font_size = 10
    title_font_size = 14
    spacing_factor = 1.0

    if optimize_for_single_page and total_content_items > 15:
        # Reduce font sizes and spacing for larger content
        base_font_size = 9
        title_font_size = 12
        spacing_factor = 0.8

    if optimize_for_single_page and total_content_items > 25:
        # Further reduce for very large content
        base_font_size = 8
        title_font_size = 11
        spacing_factor = 0.7

    # Styles
    styles = getSampleStyleSheet()
    styles.add(ParagraphStyle(
        name='InvoiceLabel',
        parent=styles['Normal'],
        fontSize=base_font_size,
        alignment=TA_LEFT,
        spaceAfter=0,
        fontName='Helvetica-Bold'
    ))
    styles.add(ParagraphStyle(
        name='InvoiceValue',
        parent=styles['Normal'],
        fontSize=base_font_size,
        alignment=TA_LEFT,
        spaceAfter=0,
        leftIndent=15  # Reduced indent for better space utilization
    ))
    styles.add(ParagraphStyle(
        name='StatementTitle',
        parent=styles['Heading1'],
        fontSize=title_font_size + 2,  # Increased font size for more prominence
        alignment=TA_LEFT,
        spaceAfter=6,  # Slightly increased spacing
        fontName='Helvetica-Bold'
    ))
    styles.add(ParagraphStyle(
        name='CompactText',
        parent=styles['Normal'],
        fontSize=base_font_size-1,  # Even smaller for very compact display
        alignment=TA_LEFT,
        spaceAfter=0,
        leading=base_font_size  # Tighter line spacing
    ))

    elements = []

    # Increased top spacing to provide more room for the header (added 10mm/1cm)
    # Apply spacing factor to reduce space for larger content
    elements.append(Spacer(1, int(50*mm * spacing_factor)))

    # Add statement title with more prominence
    elements.append(Paragraph("CUSTOMER ACCOUNT STATEMENT", styles['StatementTitle']))
    elements.append(Spacer(1, int(6*mm * spacing_factor)))  # Increased spacing after title

    # Create two separate tables for customer info and account summary
    # Customer info
    customer_name = customer_info[1] if len(customer_info) > 1 else "Unknown"
    customer_contact = customer_info[2] if len(customer_info) > 2 and customer_info[2] else "N/A"
    customer_address = customer_info[3] if len(customer_info) > 3 and customer_info[3] else "N/A"

    # Account summary
    total_invoiced = customer_info[4] if len(customer_info) > 4 else 0
    total_paid = customer_info[5] if len(customer_info) > 5 else 0
    total_unpaid = customer_info[6] if len(customer_info) > 6 else 0

    # Left side - Customer info
    customer_left = [
        [Paragraph("Customer:", styles['InvoiceLabel']),
         Paragraph(customer_name, styles['InvoiceValue'])],
        [Paragraph("Phone:", styles['InvoiceLabel']),
         Paragraph(customer_contact, styles['InvoiceValue'])],
        [Paragraph("Address:", styles['InvoiceLabel']),
         Paragraph(customer_address, styles['InvoiceValue'])]
    ]

    # Create a special style for the Total Invoiced label to ensure it stays on one line
    styles.add(ParagraphStyle(
        name='SingleLineLabel',
        parent=styles['InvoiceLabel'],
        wordWrap='CJK',  # Prevents word wrapping
        allowWidows=0,   # Prevents widows
        allowOrphans=0,  # Prevents orphans
        splitLongWords=0 # Prevents splitting long words
    ))

    # Right side - Account summary - using plain strings instead of Paragraphs for labels
    customer_right = [
        ["Total: ", Paragraph(f"${total_invoiced:.2f}", styles['InvoiceValue'])],
        ["Paid: ", Paragraph(f"${total_paid:.2f}", styles['InvoiceValue'])],
        ["Unpaid: ", Paragraph(f"${total_unpaid:.2f}", styles['InvoiceValue'])]
    ]

    # Create left and right tables
    left_table = Table(customer_left, colWidths=[25*mm, 65*mm])
    right_table = Table(customer_right, colWidths=[25*mm, 65*mm])

    # Style for both tables
    table_style = TableStyle([
        ('ALIGN', (0, 0), (0, -1), 'LEFT'),     # Labels left-aligned
        ('ALIGN', (1, 0), (1, -1), 'RIGHT'),    # Values right-aligned
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 2),
        ('TOPPADDING', (0, 0), (-1, -1), 2),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),  # Make labels bold
    ])

    left_table.setStyle(table_style)
    right_table.setStyle(table_style)

    # Combine left and right tables into a single row
    combined_table = Table([[left_table, right_table]], colWidths=[90*mm, 90*mm])
    combined_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (0, 0), 'LEFT'),    # Left table aligned left
        ('ALIGN', (1, 0), (1, 0), 'RIGHT'),   # Right table aligned right
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
    ]))

    elements.append(combined_table)
    elements.append(Spacer(1, int(6*mm * spacing_factor)))

    # Transactions table
    # Create a table for transactions
    elements.append(Paragraph("TRANSACTIONS", styles['StatementTitle']))
    elements.append(Spacer(1, int(2*mm * spacing_factor)))

    # Define the transactions table
    trans_data = [
        ["Date", "Invoice #", "Amount", "Status", "VAT"]
    ]

    # Add transactions
    for transaction in transactions:
        invoice_id = transaction[0]
        date = transaction[1]
        amount = float(transaction[2]) if transaction[2] is not None else 0.0
        status = transaction[3] if transaction[3] else "Not Paid"

        # Get VAT information if available
        vat_rate = transaction[5] if len(transaction) > 5 and transaction[5] is not None else 0
        vat_amount = transaction[6] if len(transaction) > 6 and transaction[6] is not None else 0
        vat_display = f"{vat_rate}% (${vat_amount:.2f})" if vat_rate > 0 else "N/A"

        trans_data.append([
            date,
            str(invoice_id),
            f"${amount:.2f}",
            status,
            vat_display
        ])

    # Add a summary row (unless hide_transaction_count is set)
    if not options.get('hide_transaction_count', False):
        trans_data.append(['', '', '', '', ''])
        trans_data.append(['Total Transactions:', str(len(transactions)), '', '', ''])

    # Calculate column widths for transactions table - adjust based on content size
    if optimize_for_single_page and total_content_items > 15:
        # More compact column widths for larger content
        trans_col_widths = [28*mm, 22*mm, 28*mm, 28*mm, 35*mm]
    else:
        trans_col_widths = [30*mm, 25*mm, 30*mm, 30*mm, 40*mm]

    # Create the transactions table
    trans_table = Table(trans_data, colWidths=trans_col_widths)

    # Style for transactions table
    trans_style = [
        # Header style
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),

        # Data style
        ('ALIGN', (0, 1), (0, -3), 'LEFT'),    # Date left aligned
        ('ALIGN', (1, 1), (1, -3), 'CENTER'),  # Invoice # centered
        ('ALIGN', (2, 1), (2, -3), 'RIGHT'),   # Amount right aligned
        ('ALIGN', (3, 1), (3, -3), 'CENTER'),  # Status centered
        ('ALIGN', (4, 1), (4, -3), 'RIGHT'),   # VAT right aligned

        # Grid
        ('GRID', (0, 0), (-1, len(transactions)), 0.5, colors.black),

        # Summary row
        ('SPAN', (0, -1), (1, -1)),  # Span the 'Total Transactions' label
        ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
    ]

    # Add color coding for payment status
    for i, transaction in enumerate(transactions, 1):
        status = transaction[3] if transaction[3] else "Not Paid"
        if status.lower() == 'paid':
            trans_style.append(('TEXTCOLOR', (3, i), (3, i), colors.green))
        else:
            trans_style.append(('TEXTCOLOR', (3, i), (3, i), colors.red))

    # Apply styles to transactions table
    trans_table.setStyle(TableStyle(trans_style))

    # Add the transactions table to the document
    elements.append(trans_table)
    elements.append(Spacer(1, int(5*mm * spacing_factor)))

    # Add invoice details section if available
    if invoice_details and len(invoice_details) > 0:
        elements.append(Paragraph("INVOICE DETAILS", styles['StatementTitle']))
        elements.append(Spacer(1, int(2*mm * spacing_factor)))

        # Create a table for each invoice
        for invoice_id, items in invoice_details.items():
            if not items:
                continue

            # Find the transaction for this invoice
            invoice_transaction = None
            for t in transactions:
                if t[0] == invoice_id:
                    invoice_transaction = t
                    break

            if not invoice_transaction:
                continue

            # Get invoice information
            invoice_date = invoice_transaction[1] if invoice_transaction else "Unknown"
            invoice_amount = float(invoice_transaction[2]) if invoice_transaction and invoice_transaction[2] is not None else 0.0
            invoice_status = invoice_transaction[3] if invoice_transaction and invoice_transaction[3] else "Not Paid"

            # Create a list of elements for this invoice that we'll keep together
            invoice_elements = []

            # Invoice header - use compact style for larger content
            if optimize_for_single_page and total_content_items > 15:
                invoice_elements.append(Paragraph(f"Invoice #{invoice_id} - {invoice_date} - Status: {invoice_status}", styles['CompactText']))
                invoice_elements.append(Spacer(1, 1*mm))
            else:
                invoice_elements.append(Paragraph(f"Invoice #{invoice_id} - {invoice_date} - Status: {invoice_status}", styles['InvoiceLabel']))
                invoice_elements.append(Spacer(1, 2*mm))

            # Invoice items table
            items_data = [
                ["Item", "Quantity", "Price", "Total"]
            ]

            # Add items
            for item in items:
                name = item[0] if item[0] else "Unknown"
                quantity = int(item[2]) if item[2] is not None else 0
                price = float(item[3]) if item[3] is not None else 0.0
                total = float(item[4]) if item[4] is not None else 0.0

                items_data.append([
                    name,
                    str(quantity),
                    f"${price:.2f}",
                    f"${total:.2f}"
                ])

            # Calculate column widths for items table - adjust based on content size
            if optimize_for_single_page and total_content_items > 15:
                # More compact column widths for larger content
                items_col_widths = [90*mm, 18*mm, 25*mm, 25*mm]
            else:
                items_col_widths = [100*mm, 20*mm, 30*mm, 30*mm]

            # Create the items table
            items_table = Table(items_data, colWidths=items_col_widths)

            # Style for items table
            items_style = [
                # Header style
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),

                # Data style
                ('ALIGN', (0, 1), (0, -1), 'LEFT'),    # Item left aligned
                ('ALIGN', (1, 1), (1, -1), 'CENTER'),  # Quantity centered
                ('ALIGN', (2, 1), (3, -1), 'RIGHT'),   # Prices right aligned

                # Grid
                ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
            ]

            # Apply styles to items table
            items_table.setStyle(TableStyle(items_style))

            # Add the items table to the invoice elements
            invoice_elements.append(items_table)
            invoice_elements.append(Spacer(1, int(2*mm * spacing_factor)))

            # Add invoice total - adjust column widths based on content size
            if optimize_for_single_page and total_content_items > 15:
                total_col_widths = [115*mm, 18*mm, 25*mm]
            else:
                total_col_widths = [130*mm, 20*mm, 30*mm]

            total_table = Table([['', 'Total:', f'${invoice_amount:.2f}']], colWidths=total_col_widths)
            total_table.setStyle(TableStyle([
                ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
                ('ALIGN', (2, 0), (2, 0), 'RIGHT'),
                ('FONTNAME', (1, 0), (2, 0), 'Helvetica-Bold'),
                ('LINEABOVE', (1, 0), (2, 0), 1, colors.black),
            ]))

            invoice_elements.append(total_table)

            # Add the invoice elements as a KeepTogether group to prevent page breaks within an invoice
            elements.append(KeepTogether(invoice_elements))

            # Add less space between invoices for larger content
            elements.append(Spacer(1, int(5*mm * spacing_factor)))

    # Build the document
    doc.build(elements)

    print(f"Invoice generated successfully: {filename}")
    return filename


























