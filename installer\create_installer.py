#!/usr/bin/env python3
"""
TLT Group Management System Installer Creator
Creates a self-extracting installer using Python
"""

import os
import sys
import shutil
import zipfile
import subprocess
from pathlib import Path

def main():
    print("=" * 50)
    print("TLT Group Management System Installer Creator")
    print("=" * 50)
    print()
    
    # Get the project root directory
    project_root = Path(__file__).parent.parent
    installer_dir = Path(__file__).parent
    output_dir = installer_dir / "output"
    
    # Create output directory
    output_dir.mkdir(exist_ok=True)
    
    # Clean previous builds
    for exe_file in output_dir.glob("*.exe"):
        exe_file.unlink()
    
    print("Creating installer package...")
    
    # Create a temporary directory for the installer
    temp_dir = installer_dir / "temp"
    if temp_dir.exists():
        shutil.rmtree(temp_dir)
    temp_dir.mkdir()
    
    try:
        # Copy application files
        copy_app_files(project_root, temp_dir)
        
        # Create the installer archive
        installer_zip = output_dir / "TLT_Group_Management_System.zip"
        create_zip_installer(temp_dir, installer_zip)
        
        # Try to create executable installer
        try:
            create_exe_installer(temp_dir, output_dir)
            print("\n✓ Executable installer created successfully!")
        except Exception as e:
            print(f"\n⚠ Could not create executable installer: {e}")
            print("✓ ZIP installer created as fallback")
        
        print(f"\nInstaller files created in: {output_dir}")
        print("\nFiles created:")
        for file in output_dir.iterdir():
            if file.is_file():
                size_mb = file.stat().st_size / (1024 * 1024)
                print(f"  - {file.name} ({size_mb:.1f} MB)")
        
    finally:
        # Clean up temporary directory
        if temp_dir.exists():
            shutil.rmtree(temp_dir)

def copy_app_files(project_root, temp_dir):
    """Copy application files to temporary directory"""
    
    # Files and directories to include
    include_items = [
        "main.py",
        "requirements.txt",
        "README.md",
        "modules/",
        "resources/",
        "config/",
    ]
    
    # Create logs directory
    (temp_dir / "logs").mkdir(exist_ok=True)
    (temp_dir / "logs" / ".gitkeep").touch()
    
    for item in include_items:
        source = project_root / item
        dest = temp_dir / item
        
        if source.is_file():
            dest.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(source, dest)
            print(f"  Copied: {item}")
        elif source.is_dir():
            shutil.copytree(source, dest, dirs_exist_ok=True)
            print(f"  Copied: {item}")
        else:
            print(f"  Warning: {item} not found")
    
    # Copy installer files
    installer_files = [
        "run_app.bat",
        "license.txt",
        "readme.txt"
    ]
    
    installer_dir = Path(__file__).parent
    for file in installer_files:
        source = installer_dir / file
        if source.exists():
            shutil.copy2(source, temp_dir / file)
            print(f"  Copied: {file}")

def create_zip_installer(temp_dir, output_file):
    """Create ZIP installer"""
    with zipfile.ZipFile(output_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in temp_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(temp_dir)
                zipf.write(file_path, arcname)

def create_exe_installer(temp_dir, output_dir):
    """Create executable installer using PyInstaller if available"""
    
    # Check if PyInstaller is available
    try:
        subprocess.run([sys.executable, "-c", "import PyInstaller"], 
                      check=True, capture_output=True)
    except subprocess.CalledProcessError:
        raise Exception("PyInstaller not available. Install with: pip install pyinstaller")
    
    # Create installer script
    installer_script = temp_dir / "installer.py"
    create_installer_script(installer_script, temp_dir)
    
    # Build executable
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed",
        "--name", "TLT_Group_Management_System_Setup",
        "--icon", str(temp_dir / "resources" / "TLT icon.ico"),
        str(installer_script)
    ]
    
    subprocess.run(cmd, cwd=temp_dir, check=True)
    
    # Move the executable to output directory
    exe_source = temp_dir / "dist" / "TLT_Group_Management_System_Setup.exe"
    exe_dest = output_dir / "TLT_Group_Management_System_Setup.exe"
    shutil.move(exe_source, exe_dest)

def create_installer_script(script_path, temp_dir):
    """Create the installer script"""
    script_content = '''
import os
import sys
import shutil
import tkinter as tk
from tkinter import messagebox, filedialog
from pathlib import Path

def main():
    # Simple GUI installer
    root = tk.Tk()
    root.title("TLT Group Management System Installer")
    root.geometry("500x300")
    
    # Installation logic here
    install_dir = "C:\\\\Program Files\\\\TLT Group\\\\Management System"
    
    def install():
        try:
            # Create installation directory
            Path(install_dir).mkdir(parents=True, exist_ok=True)
            
            # Copy files (embedded in the executable)
            # This would need to be implemented with actual file extraction
            
            messagebox.showinfo("Success", "Installation completed successfully!")
            root.quit()
        except Exception as e:
            messagebox.showerror("Error", f"Installation failed: {e}")
    
    tk.Label(root, text="TLT Group Management System", font=("Arial", 16)).pack(pady=20)
    tk.Label(root, text="Click Install to begin installation").pack(pady=10)
    tk.Button(root, text="Install", command=install, bg="green", fg="white").pack(pady=20)
    
    root.mainloop()

if __name__ == "__main__":
    main()
'''
    
    with open(script_path, 'w') as f:
        f.write(script_content)

if __name__ == "__main__":
    main()
