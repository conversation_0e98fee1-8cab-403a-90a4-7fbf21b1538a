#!/usr/bin/env python3
"""
Test script for the new multiple deposits functionality
"""

import sys
import os
from datetime import datetime

# Add the modules directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from database import Database

def test_deposits():
    """Test the new deposits functionality"""
    print("Testing Multiple Deposits Functionality")
    print("=" * 50)
    
    # Initialize database
    db = Database()
    
    # Test 1: Create the deposits table
    print("\n1. Testing database table creation...")
    try:
        # The table should be created automatically when Database() is initialized
        db.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='invoice_deposits'")
        result = db.cursor.fetchone()
        if result:
            print("✅ invoice_deposits table exists")
        else:
            print("❌ invoice_deposits table not found")
            return False
    except Exception as e:
        print(f"❌ Error checking table: {e}")
        return False
    
    # Test 2: Check if we have any existing invoices to test with
    print("\n2. Checking for existing invoices...")
    try:
        db.cursor.execute("SELECT id, customer_id, total FROM invoices LIMIT 5")
        invoices = db.cursor.fetchall()
        if invoices:
            print(f"✅ Found {len(invoices)} existing invoices")
            for invoice in invoices:
                print(f"   Invoice #{invoice[0]}: Customer {invoice[1]}, Total ${invoice[2]:.2f}")
        else:
            print("⚠️  No existing invoices found. Creating a test invoice...")
            # Create a test invoice
            test_invoice_id = create_test_invoice(db)
            if test_invoice_id:
                invoices = [(test_invoice_id, 1, 100.0)]
                print(f"✅ Created test invoice #{test_invoice_id}")
            else:
                print("❌ Failed to create test invoice")
                return False
    except Exception as e:
        print(f"❌ Error checking invoices: {e}")
        return False
    
    # Test 3: Add multiple deposits to the first invoice
    test_invoice_id = invoices[0][0]
    print(f"\n3. Testing multiple deposits for invoice #{test_invoice_id}...")
    
    try:
        # Add first deposit
        deposit1_id = db.add_invoice_deposit(
            invoice_id=test_invoice_id,
            amount=25.00,
            date="2025-01-01",
            description="First deposit"
        )
        print(f"✅ Added first deposit: ID {deposit1_id}")
        
        # Add second deposit
        deposit2_id = db.add_invoice_deposit(
            invoice_id=test_invoice_id,
            amount=30.00,
            date="2025-01-15",
            description="Second deposit"
        )
        print(f"✅ Added second deposit: ID {deposit2_id}")
        
        # Add third deposit
        deposit3_id = db.add_invoice_deposit(
            invoice_id=test_invoice_id,
            amount=20.00,
            date="2025-01-30",
            description="Third deposit"
        )
        print(f"✅ Added third deposit: ID {deposit3_id}")
        
    except Exception as e:
        print(f"❌ Error adding deposits: {e}")
        return False
    
    # Test 4: Retrieve all deposits for the invoice
    print(f"\n4. Testing deposit retrieval for invoice #{test_invoice_id}...")
    try:
        deposits = db.get_invoice_deposits(test_invoice_id)
        print(f"✅ Retrieved {len(deposits)} deposits:")
        for deposit in deposits:
            deposit_id, amount, date, description, created_at = deposit
            print(f"   Deposit #{deposit_id}: ${amount:.2f} on {date} - {description}")
    except Exception as e:
        print(f"❌ Error retrieving deposits: {e}")
        return False
    
    # Test 5: Calculate total deposits
    print(f"\n5. Testing total deposits calculation...")
    try:
        total_deposits = db.get_total_deposits_for_invoice(test_invoice_id)
        print(f"✅ Total deposits for invoice #{test_invoice_id}: ${total_deposits:.2f}")
        expected_total = 25.00 + 30.00 + 20.00
        if abs(total_deposits - expected_total) < 0.01:
            print(f"✅ Total matches expected amount: ${expected_total:.2f}")
        else:
            print(f"❌ Total mismatch. Expected: ${expected_total:.2f}, Got: ${total_deposits:.2f}")
    except Exception as e:
        print(f"❌ Error calculating total deposits: {e}")
        return False
    
    # Test 6: Remove a deposit
    print(f"\n6. Testing deposit removal...")
    try:
        # Remove the second deposit
        success = db.remove_invoice_deposit(deposit2_id)
        if success:
            print(f"✅ Successfully removed deposit #{deposit2_id}")
            
            # Check remaining deposits
            remaining_deposits = db.get_invoice_deposits(test_invoice_id)
            print(f"✅ Remaining deposits: {len(remaining_deposits)}")
            
            # Check new total
            new_total = db.get_total_deposits_for_invoice(test_invoice_id)
            expected_new_total = 25.00 + 20.00  # First and third deposits
            print(f"✅ New total deposits: ${new_total:.2f}")
            
            if abs(new_total - expected_new_total) < 0.01:
                print(f"✅ New total matches expected: ${expected_new_total:.2f}")
            else:
                print(f"❌ New total mismatch. Expected: ${expected_new_total:.2f}, Got: ${new_total:.2f}")
        else:
            print(f"❌ Failed to remove deposit #{deposit2_id}")
    except Exception as e:
        print(f"❌ Error removing deposit: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("✅ All deposit functionality tests passed!")
    print("\nThe new multiple deposits feature is working correctly.")
    print("\nHow to use in the application:")
    print("1. Edit an existing invoice")
    print("2. Enter a deposit amount and date")
    print("3. Click 'Add Deposit' to add it to existing deposits")
    print("4. View all deposits in the deposits list")
    print("5. Remove deposits if needed")
    
    return True

def create_test_invoice(db):
    """Create a test invoice for testing deposits"""
    try:
        # Check if we have any customers
        db.cursor.execute("SELECT id FROM customers LIMIT 1")
        customer = db.cursor.fetchone()
        
        if not customer:
            # Create a test customer
            db.cursor.execute("""
                INSERT INTO customers (name, email, phone, address)
                VALUES (?, ?, ?, ?)
            """, ("Test Customer", "<EMAIL>", "************", "123 Test St"))
            customer_id = db.cursor.lastrowid
        else:
            customer_id = customer[0]
        
        # Create a test invoice
        invoice_data = {
            'customer_id': customer_id,
            'date': datetime.now().strftime('%Y-%m-%d'),
            'total': 100.0,
            'vat_rate': 0.0,
            'status': 'Pending',
            'notes': 'Test invoice for deposits',
            'discount_amount': 0.0,
            'discount_type': 'amount',
            'deposit_amount': 0.0,
            'deposit_date': None
        }
        
        invoice_id = db.create_invoice(invoice_data, [])
        db.conn.commit()
        return invoice_id
        
    except Exception as e:
        print(f"Error creating test invoice: {e}")
        return None

if __name__ == "__main__":
    test_deposits()
