@echo off
title Building TLT Group Management System Installer with NSIS
echo ========================================
echo Building TLT Group Management System Installer
echo Using NSIS (Nullsoft Scriptable Install System)
echo ========================================
echo.

REM Check if NSIS is installed
set "NSIS_PATH=C:\Program Files (x86)\NSIS\makensis.exe"

if not exist "%NSIS_PATH%" (
    echo NSIS not found at: %NSIS_PATH%
    echo Please install NSIS from: https://nsis.sourceforge.io/Download
    pause
    exit /b 1
)
echo Found NSIS at: %NSIS_PATH%
echo.

REM Check if required files exist
echo Checking required files...
set "MISSING_FILES="

if not exist "..\main.py" (
    echo ❌ Missing: ..\main.py
    set "MISSING_FILES=1"
)

if not exist "..\requirements.txt" (
    echo ❌ Missing: ..\requirements.txt
    set "MISSING_FILES=1"
)

if not exist "..\resources\TLT icon.ico" (
    echo ❌ Missing: ..\resources\TLT icon.ico
    set "MISSING_FILES=1"
)

if not exist "license.txt" (
    echo ❌ Missing: license.txt
    set "MISSING_FILES=1"
)

if not exist "run_app.bat" (
    echo ❌ Missing: run_app.bat
    set "MISSING_FILES=1"
)

if not exist "tlt_installer.nsi" (
    echo ❌ Missing: tlt_installer.nsi
    set "MISSING_FILES=1"
)

if defined MISSING_FILES (
    echo.
    echo Some required files are missing. Please ensure all files are present.
    pause
    exit /b 1
)

echo ✅ All required files found
echo.

REM Create output directory if it doesn't exist
if not exist "output" mkdir output

REM Clean previous builds
if exist "output\*.exe" (
    echo Cleaning previous builds...
    del /q "output\*.exe"
)

echo Compiling installer with NSIS...
echo.

REM Enable delayed variable expansion for the loop
setlocal EnableDelayedExpansion

REM Compile the installer
"%NSIS_PATH%" /V3 "tlt_installer.nsi"

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✅ Installer built successfully!
    echo ========================================
    echo.
    
    REM Check if the output file exists
    if exist "TLT_Group_Management_System_Setup.exe" (
        REM Move to output directory
        move "TLT_Group_Management_System_Setup.exe" "output\"
        
        REM Get file size
        for %%A in ("output\TLT_Group_Management_System_Setup.exe") do (
            set "FILE_SIZE=%%~zA"
            set /a "SIZE_MB=!FILE_SIZE! / 1048576"
        )
        
        echo Output file: output\TLT_Group_Management_System_Setup.exe
        echo File size: !SIZE_MB! MB
        echo.
        echo 📋 Installer Features:
        echo ✅ Professional installation wizard
        echo ✅ Automatic Python dependency installation
        echo ✅ Desktop and Start Menu shortcuts
        echo ✅ System requirements checking
        echo ✅ Clean uninstallation
        echo ✅ Multi-language support (English/French)
        echo ✅ Add/Remove Programs integration
        echo.
        echo 🚀 Ready for distribution!
        echo.
        
        REM Open output folder
        explorer "output"
    ) else (
        echo ❌ Output file not found. Build may have failed.
    )
) else (
    echo.
    echo ========================================
    echo ❌ Build failed!
    echo ========================================
    echo.
    echo Please check the error messages above and fix any issues.
    echo.
    echo Common issues:
    echo - Missing source files
    echo - Incorrect file paths in the NSI script
    echo - NSIS syntax errors
    echo - Insufficient permissions
    echo.
)

echo.
echo Build process completed.
pause
