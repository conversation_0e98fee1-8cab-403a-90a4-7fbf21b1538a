#!/usr/bin/env python3
"""
TLT Group Management System - System Check
Verifies system requirements and installation integrity
"""

import sys
import os
import platform
import subprocess
import importlib
from pathlib import Path

def main():
    print("=" * 60)
    print("TLT GROUP MANAGEMENT SYSTEM - SYSTEM CHECK")
    print("=" * 60)
    print()
    
    all_checks_passed = True
    
    # System Information
    print("📋 SYSTEM INFORMATION")
    print("-" * 30)
    print(f"Operating System: {platform.system()} {platform.release()}")
    print(f"Architecture: {platform.machine()}")
    print(f"Python Version: {sys.version}")
    print(f"Python Executable: {sys.executable}")
    print()
    
    # Check Python version
    print("🐍 PYTHON VERSION CHECK")
    print("-" * 30)
    if sys.version_info >= (3, 8):
        print("✅ Python version is compatible (3.8+)")
    else:
        print("❌ Python version is too old. Requires Python 3.8 or higher")
        all_checks_passed = False
    print()
    
    # Check required modules
    print("📦 REQUIRED MODULES CHECK")
    print("-" * 30)
    
    required_modules = [
        ('tkinter', 'GUI framework'),
        ('sqlite3', 'Database support'),
        ('reportlab', 'PDF generation'),
        ('PIL', 'Image processing (Pillow)'),
        ('datetime', 'Date/time handling'),
        ('pathlib', 'Path operations'),
        ('json', 'JSON handling'),
        ('csv', 'CSV file support'),
        ('os', 'Operating system interface'),
        ('sys', 'System-specific parameters'),
    ]
    
    for module_name, description in required_modules:
        try:
            importlib.import_module(module_name)
            print(f"✅ {module_name:<15} - {description}")
        except ImportError:
            print(f"❌ {module_name:<15} - {description} (MISSING)")
            all_checks_passed = False
    print()
    
    # Check application files
    print("📁 APPLICATION FILES CHECK")
    print("-" * 30)
    
    app_root = Path(__file__).parent.parent
    required_files = [
        'main.py',
        'requirements.txt',
        'modules/database/db_manager.py',
        'modules/ui/main_window.py',
        'modules/ui/customers.py',
        'modules/ui/sales.py',
        'modules/ui/inventory.py',
        'modules/ui/suppliers.py',
        'modules/utils/invoice_template.py',
        'resources/TLT icon.ico',
        'config/settings.json',
    ]
    
    for file_path in required_files:
        full_path = app_root / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (MISSING)")
            all_checks_passed = False
    print()
    
    # Check write permissions
    print("🔐 PERMISSIONS CHECK")
    print("-" * 30)
    
    test_dirs = [
        app_root / 'logs',
        app_root / 'config',
        Path.home() / 'Documents' / 'TLT_Invoices',
    ]
    
    for test_dir in test_dirs:
        try:
            test_dir.mkdir(parents=True, exist_ok=True)
            test_file = test_dir / 'test_write.tmp'
            test_file.write_text('test')
            test_file.unlink()
            print(f"✅ Write access to {test_dir}")
        except Exception as e:
            print(f"❌ No write access to {test_dir}: {e}")
            all_checks_passed = False
    print()
    
    # Check database
    print("🗄️ DATABASE CHECK")
    print("-" * 30)
    
    try:
        sys.path.insert(0, str(app_root))
        from modules.database.db_manager import DatabaseManager
        
        db = DatabaseManager()
        db.create_tables()
        print("✅ Database connection successful")
        print("✅ Database tables created/verified")
        
        # Test basic operations
        test_customer = db.add_customer("Test Customer", "<EMAIL>", "123 Test St", "555-0123")
        if test_customer:
            db.delete_customer(test_customer)
            print("✅ Database operations working")
        else:
            print("⚠️ Database operations may have issues")
            
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        all_checks_passed = False
    print()
    
    # Performance check
    print("⚡ PERFORMANCE CHECK")
    print("-" * 30)
    
    try:
        import time
        start_time = time.time()
        
        # Simulate application startup
        for i in range(1000):
            _ = i * i
        
        end_time = time.time()
        startup_time = (end_time - start_time) * 1000
        
        if startup_time < 100:
            print(f"✅ System performance good ({startup_time:.1f}ms)")
        elif startup_time < 500:
            print(f"⚠️ System performance acceptable ({startup_time:.1f}ms)")
        else:
            print(f"❌ System performance poor ({startup_time:.1f}ms)")
            
    except Exception as e:
        print(f"⚠️ Performance check failed: {e}")
    print()
    
    # Final result
    print("=" * 60)
    if all_checks_passed:
        print("🎉 ALL CHECKS PASSED!")
        print("The TLT Group Management System is ready to use.")
        print()
        print("To start the application:")
        print("1. Double-click the desktop shortcut, or")
        print("2. Run 'python main.py' from the application directory")
    else:
        print("❌ SOME CHECKS FAILED!")
        print("Please address the issues above before using the application.")
        print()
        print("Common solutions:")
        print("- Install missing Python packages: pip install -r requirements.txt")
        print("- Run as administrator for permission issues")
        print("- Reinstall the application if files are missing")
    print("=" * 60)
    print()
    
    return 0 if all_checks_passed else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        input("Press Enter to exit...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nSystem check cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error during system check: {e}")
        input("Press Enter to exit...")
        sys.exit(1)
