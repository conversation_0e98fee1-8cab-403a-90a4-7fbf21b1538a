from .base_module import BaseModule
import tkinter as tk
from tkinter import ttk, messagebox
import logging
import os
# Try to import from the new barcode generator first
try:
    from ..utils.barcode_generator_new import generate_barcode_for_tk, save_barcode_image
    # Define generate_random_barcode function since it's not in the new module
    def generate_random_barcode(prefix="", length=12):
        import random
        import string
        # Generate random digits
        digits = ''.join(random.choices(string.digits, k=length))
        # Add prefix if provided
        return f"{prefix}{digits}"
except ImportError:
    # Fall back to the original module if the new one is not available
    from ..utils.barcode_generator import generate_barcode_for_tk, generate_random_barcode, save_barcode_image
from ..utils.label_generator import generate_barcode_label
from ..utils.table_customizer import TableCustomizer

# Get logger for this module
logger = logging.getLogger(__name__)

class InventoryModule(BaseModule):
    def __init__(self, parent, db):
        super().__init__(parent, db)
        try:
            # Initialize status variable for feedback messages
            self.status_var = tk.StringVar(value="Ready")

            # Store reference to the root window for dialogs
            self.root = self.get_root_window(parent)

            self.settings = self.load_settings()
            self.create_inventory_interface()
            self.refresh_inventory()

            # Set up auto-refresh
            self.refresh_interval = 60000  # 60 seconds
            self.setup_auto_refresh()

            # Bind to inventory changed event (triggered by sales module)
            self.parent.bind('<<InventoryChanged>>', self.on_inventory_changed)
        except Exception as e:
            logger.error(f"Failed to initialize inventory module: {e}")
            messagebox.showerror("Error", "Failed to initialize inventory module")

    def get_root_window(self, widget):
        """Get the root window from a widget

        Args:
            widget: A tkinter widget

        Returns:
            The root window
        """
        parent = widget.master
        while parent.master is not None:
            parent = parent.master
        return parent

    def load_settings(self):
        """Load settings from database"""
        try:
            # First try to get settings from database
            self.db.cursor.execute("""
                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT
                )
            """)

            # Get all settings
            self.db.cursor.execute("SELECT key, value FROM settings")
            settings = dict(self.db.cursor.fetchall())

            # Return settings with defaults if not found
            return {
                'low_stock': int(settings.get('low_stock', 10)),
                'currency': settings.get('currency', '$'),
                'tax_rate': float(settings.get('tax_rate', 15.0))
            }
        except Exception as e:
            logger.error(f"Failed to load settings: {e}")
            return {
                'low_stock': 10,
                'currency': '$',
                'tax_rate': 15.0
            }

    def create_inventory_interface(self):
        # Control buttons frame
        control_frame = ttk.Frame(self.frame)
        control_frame.pack(fill="x", padx=5, pady=5)

        ttk.Button(control_frame, text="Edit Selected",
                  command=self.edit_product).pack(side="left", padx=5)
        ttk.Button(control_frame, text="Delete Selected",
                  command=self.delete_product).pack(side="left", padx=5)
        ttk.Button(control_frame, text="Refresh",
                  command=self.refresh_inventory).pack(side="left", padx=5)

        # Add low stock threshold setting
        settings_frame = ttk.LabelFrame(self.frame, text="Inventory Settings")
        settings_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(settings_frame, text="Low Stock Alert Threshold:").pack(side="left", padx=5)
        self.threshold_var = tk.StringVar(value="10")
        ttk.Entry(settings_frame, textvariable=self.threshold_var, width=10).pack(side="left")

        ttk.Button(settings_frame, text="Check Low Stock",
                  command=self.check_low_stock).pack(side="right", padx=5)

        # Add search frame
        search_frame = ttk.Frame(self.frame)
        search_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(search_frame, text="Search:").pack(side="left", padx=5)
        self.search_var = tk.StringVar()
        self.search_var.trace_add('write', self.on_search)
        ttk.Entry(search_frame, textvariable=self.search_var).pack(side="left", fill="x", expand=True)

        # Product entry form
        entry_frame = ttk.LabelFrame(self.frame, text="Product Details")
        entry_frame.pack(fill="x", padx=5, pady=5)

        # Barcode management section
        barcode_frame = ttk.LabelFrame(entry_frame, text="Barcode Management")
        barcode_frame.pack(fill="x", padx=5, pady=5)

        # Barcode generation
        ttk.Label(barcode_frame, text="Barcode:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.barcode_var = tk.StringVar()
        barcode_entry = ttk.Entry(barcode_frame, textvariable=self.barcode_var, width=30)
        barcode_entry.grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        # Barcode buttons
        barcode_buttons = ttk.Frame(barcode_frame)
        barcode_buttons.grid(row=0, column=2, padx=5, pady=5)

        ttk.Button(barcode_buttons, text="Generate",
                  command=self.generate_barcode).pack(side="left", padx=2)
        ttk.Button(barcode_buttons, text="Save",
                  command=self.save_barcode).pack(side="left", padx=2)
        ttk.Button(barcode_buttons, text="Print Label",
                  command=self.print_barcode_label).pack(side="left", padx=2)

        # Barcode preview - make it larger and more visible
        self.barcode_preview_frame = ttk.LabelFrame(barcode_frame, text="Barcode Preview")
        self.barcode_preview_frame.grid(row=1, column=0, columnspan=3, padx=5, pady=5, sticky="ew")

        # Create a fixed size frame for the preview to prevent layout changes
        preview_container = ttk.Frame(self.barcode_preview_frame, width=300, height=120)
        preview_container.pack(padx=10, pady=10)
        preview_container.pack_propagate(False)  # Prevent the frame from resizing to fit contents

        self.barcode_preview_label = ttk.Label(preview_container, text="No barcode generated")
        self.barcode_preview_label.pack(expand=True)

        # Store the barcode image reference to prevent garbage collection
        self.barcode_image = None

        # Product details with labels aligned left
        fields = [
            ("Barcode:", "product_code"),  # Renamed from Product Code to Barcode
            ("Product Name:", "name"),
            ("Quantity:", "quantity"),
            ("Unit Price:", "price"),
            ("Cost Price:", "cost_price"),
            ("Category:", "category"),
            ("Minimum Stock:", "min_stock"),
            ("Description:", "description")
        ]
        self.entries = {}

        for label, key in fields:
            frame = ttk.Frame(entry_frame)
            frame.pack(fill="x", padx=5, pady=2)
            ttk.Label(frame, text=label, width=15).pack(side="left")
            if key == "description":
                self.entries[key] = tk.Text(frame, height=3, width=40)
            else:
                self.entries[key] = ttk.Entry(frame, width=40)
            self.entries[key].pack(side="left", fill="x", expand=True)

        # Buttons frame
        btn_frame = ttk.Frame(entry_frame)
        btn_frame.pack(fill="x", padx=5, pady=5)

        ttk.Button(btn_frame, text="Add New",
                  command=self.add_product).pack(side="left", padx=5)
        ttk.Button(btn_frame, text="Save Changes",
                  command=self.save_product).pack(side="left", padx=5)
        ttk.Button(btn_frame, text="Clear",
                  command=self.clear_entries).pack(side="left", padx=5)

        # Create products table with scrollbar
        table_frame = ttk.Frame(self.frame)
        table_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Add scrollbars
        y_scroll = ttk.Scrollbar(table_frame)
        y_scroll.pack(side="right", fill="y")
        x_scroll = ttk.Scrollbar(table_frame, orient="horizontal")
        x_scroll.pack(side="bottom", fill="x")

        # Create tree with scrollbars - order must match data insertion order
        columns = ('barcode', 'name', 'quantity', 'price', 'cost price', 'category', 'min stock', 'description')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings',
                                yscrollcommand=y_scroll.set,
                                xscrollcommand=x_scroll.set)

        # Configure scrollbars
        y_scroll.config(command=self.tree.yview)
        x_scroll.config(command=self.tree.xview)

        # Configure columns
        column_widths = {
            'barcode': 150, 'name': 200, 'quantity': 80,
            'price': 100, 'cost price': 100, 'category': 100,
            'min stock': 80, 'description': 200
        }

        # Set proper column headings
        column_headings = {
            'barcode': 'Barcode',
            'name': 'Name',
            'quantity': 'Quantity',
            'price': 'Price',
            'cost price': 'Cost Price',
            'category': 'Category',
            'min stock': 'Min Stock',
            'description': 'Description'
        }

        for col, width in column_widths.items():
            self.tree.column(col, width=width, minwidth=width)
            self.tree.heading(col, text=column_headings[col],
                            command=lambda c=col: self.sort_tree(c))

        self.tree.pack(fill="both", expand=True)

        # Add column customization
        self.table_customizer = TableCustomizer(self.tree, "inventory")

        # Add status bar at the bottom
        status_frame = ttk.Frame(self.frame)
        status_frame.pack(fill="x", side="bottom", padx=5, pady=2)

        ttk.Label(status_frame, textvariable=self.status_var).pack(side="left", padx=5)
        self.tree.bind('<Double-1>', self.on_double_click)

        # Configure tag for low stock items
        self.tree.tag_configure('low_stock', foreground='red')

    def delete_product(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a product to delete")
            return

        if messagebox.askyesno("Confirm", "Delete selected product? This cannot be undone."):
            try:
                item = self.tree.item(selected[0])
                # Get product ID from tag
                product_id = item['tags'][0] if item['tags'] else None
                if not product_id:
                    messagebox.showerror("Error", "Could not find product ID")
                    return

                self.db.delete_product(int(product_id))
                self.tree.delete(selected[0])
                self.clear_entries()
                messagebox.showinfo("Success", "Product deleted successfully")
            except Exception as e:
                logger.error(f"Database error in delete_product: {e}")
                messagebox.showerror("Error", str(e))

    def add_product(self):
        try:
            data = self._get_form_data()
            if not self._validate_product_data(data):
                return

            # Add product using database method
            self.db.add_product(
                name=data['name'],
                quantity=int(data['quantity']),
                unit_price=float(data['price']),
                category=data['category'],
                min_stock=int(data['min_stock']),
                cost_price=float(data['cost_price']),
                product_code=data['product_code'],
                description=data['description']
            )

            self.clear_entries()
            self.refresh_inventory()
            messagebox.showinfo("Success", "Product added successfully")

        except Exception as e:
            logger.error(f"Database error in add_product: {e}")
            messagebox.showerror("Error", str(e))

    def save_product(self):
        try:
            selected = self.tree.selection()
            if not selected:
                messagebox.showwarning("Warning", "Please select a product to update")
                return

            data = self._get_form_data()
            if not self._validate_product_data(data):
                return

            item = self.tree.item(selected[0])
            # Get product ID from tag
            product_id = item['tags'][0] if item['tags'] else None
            if not product_id:
                messagebox.showerror("Error", "Could not find product ID")
                return

            # Update product using database method
            self.db.update_product(int(product_id), {
                'name': data['name'],
                'quantity': int(data['quantity']) if data['quantity'] else 0,
                'price': float(data['price']) if data['price'] else 0.0,
                'cost_price': float(data['cost_price']) if data['cost_price'] else 0.0,
                'category': data['category'],
                'min_stock': int(data['min_stock']) if data['min_stock'] else 0,
                'product_code': data['product_code'],
                'description': data['description']
            })

            self.clear_entries()
            self.refresh_inventory()
            messagebox.showinfo("Success", "Product updated successfully")

        except Exception as e:
            logger.error(f"Database error in save_product: {e}")
            messagebox.showerror("Error", str(e))

    def clear_entries(self):
        for entry in self.entries.values():
            if isinstance(entry, tk.Text):
                entry.delete('1.0', tk.END)
            else:
                entry.delete(0, tk.END)

    def on_search(self, *_):
        query = self.search_var.get()
        try:
            # Use the correct table name (inventory, not products)
            self.db.cursor.execute("""
                SELECT id, product_code, name, quantity, unit_price, cost_price,
                       category, min_stock, description
                FROM inventory
                WHERE name LIKE ? OR product_code LIKE ?
                ORDER BY name
            """, (f"%{query}%", f"%{query}%"))
            results = self.db.cursor.fetchall()
            self.update_product_list(results)
        except Exception as e:
            logger.error(f"Database error in on_search: {e}")
            messagebox.showerror("Database Error", "Failed to search products")

    def update_product_list(self, products):
        """Update the product list display with color coding"""
        self.tree.delete(*self.tree.get_children())

        for product in products:
            product_id = product[0]
            quantity = product[3] or 0
            min_stock = product[7] or 0

            # Determine if this is low stock
            tag_list = [str(product_id)]
            if min_stock > 0 and quantity <= min_stock:
                tag_list.append('low_stock')

            # Insert with values excluding the ID column
            self.tree.insert('', 'end', values=(
                product[1],  # barcode
                product[2],  # name
                product[3],  # quantity
                f"${product[4]:.2f}" if product[4] else "$0.00",  # unit_price
                f"${product[5]:.2f}" if product[5] else "$0.00",  # cost_price
                product[6],  # category
                product[7],  # min_stock
                product[8]   # description
            ), tags=tuple(tag_list))

    def check_low_stock(self):
        """Check for items below their minimum stock level"""
        try:
            # Use the correct table name (inventory, not products)
            self.db.cursor.execute(
                """SELECT id, name, quantity, min_stock
                   FROM inventory
                   WHERE min_stock > 0 AND quantity <= min_stock"""
            )
            low_stock = self.db.cursor.fetchall()

            if low_stock:
                message = "Low stock items:\n\n"
                for item in low_stock:
                    message += f"• {item[1]}: {item[2]} remaining (Min: {item[3]})\n"
                messagebox.showwarning("Low Stock Alert", message)
            else:
                messagebox.showinfo("Stock Status", "No items below their minimum stock level")
        except Exception as e:
            logger.error(f"Database error in check_low_stock: {e}")
            messagebox.showerror("Database Error", "Failed to check low stock items")

    def edit_product(self):
        """Load selected product into entry fields"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a product to edit")
            return

        # Get selected item values
        item = self.tree.item(selected[0])

        # Get product ID from tag
        product_id = item['tags'][0] if item['tags'] else None
        if not product_id:
            messagebox.showerror("Error", "Could not find product ID")
            return

        # Get full product details from database using the ID
        product = self.db.get_product_by_id(int(product_id))
        if not product:
            messagebox.showerror("Error", "Product not found in database")
            return

        # Map fields to product data
        field_data = {
            'product_code': product[1],  # barcode
            'name': product[2],          # name
            'quantity': product[3],       # quantity
            'price': product[4],          # unit_price
            'cost_price': product[5],     # cost_price
            'category': product[6],       # category
            'min_stock': product[7],      # min_stock
            'description': product[8]     # description
        }

        # Populate entry fields
        for field, value in field_data.items():
            if field == 'description':
                self.entries[field].delete('1.0', tk.END)
                self.entries[field].insert('1.0', value or '')
            else:
                self.entries[field].delete(0, tk.END)
                self.entries[field].insert(0, value or '')

    def _get_form_data(self):
        """Get and validate form data"""
        data = {
            'name': self.entries['name'].get().strip(),
            'quantity': self.entries['quantity'].get().strip(),
            'price': self.entries['price'].get().strip(),
            'cost_price': self.entries['cost_price'].get().strip(),
            'category': self.entries['category'].get().strip(),
            'min_stock': self.entries['min_stock'].get().strip(),
            'product_code': self.entries['product_code'].get().strip(),
            'description': self.entries['description'].get('1.0', tk.END).strip()
        }
        return data

    def _validate_product_data(self, data):
        """Validate product data before saving"""
        if not data['name']:
            messagebox.showerror("Error", "Product name is required")
            return False

        # Check for required fields for sales
        if not data['quantity'] or float(data['quantity']) <= 0:
            messagebox.showwarning("Warning", "Quantity must be greater than 0 for the product to appear in sales")
            # Don't return False, just warn the user

        if not data['price'] or float(data['price']) <= 0:
            messagebox.showwarning("Warning", "Unit price must be greater than 0 for the product to appear in sales")
            # Don't return False, just warn the user

        try:
            # Convert to appropriate types with defaults if empty
            if data['quantity']: float(data['quantity'])
            if data['price']: float(data['price'])
            if data['cost_price']: float(data['cost_price'])
            if data['min_stock']: int(data['min_stock'])
        except ValueError:
            messagebox.showerror("Error", "Please enter valid numbers for quantity, prices and minimum stock")
            return False

        return True

    def sort_tree(self, col):
        """Sort treeview by column"""
        items = [(self.tree.set(item, col), item) for item in self.tree.get_children('')]
        items.sort()

        for index, (_, item) in enumerate(items):
            self.tree.move(item, '', index)

    def on_double_click(self, _):
        """Handle double click on tree item"""
        selected = self.tree.selection()
        if not selected:  # Check if any item is selected
            return
        self.edit_product()

    def generate_barcode(self):
        """Generate a random barcode or use the entered one"""
        try:
            # Get barcode from entry or generate a random one
            barcode = self.barcode_var.get().strip()
            if not barcode:
                barcode = generate_random_barcode(12)  # Generate 12-digit barcode
                self.barcode_var.set(barcode)

            # Update the product_code entry with the generated barcode
            if 'product_code' in self.entries:
                self.entries['product_code'].delete(0, tk.END)
                self.entries['product_code'].insert(0, barcode)

            # Generate barcode image with larger dimensions for better visibility
            self.barcode_image = generate_barcode_for_tk(barcode, width=280, height=100)

            # Check if the preview container exists and is accessible
            if hasattr(self, 'barcode_preview_label') and self.barcode_preview_label and hasattr(self.barcode_preview_label, 'master'):
                preview_container = self.barcode_preview_label.master

                # Clear previous content in the preview container
                for widget in preview_container.winfo_children():
                    widget.destroy()

                # Display barcode image
                if self.barcode_image:
                    # Create a new label for the image
                    image_label = ttk.Label(preview_container, image=self.barcode_image)
                    image_label.pack(padx=5, pady=5)

                    # Add the barcode text below the image
                    text_label = ttk.Label(preview_container, text=barcode, font=("Arial", 10, "bold"))
                    text_label.pack(padx=5)

                    # Update status
                    self.status_var.set(f"Barcode generated: {barcode}")
                else:
                    error_label = ttk.Label(preview_container, text="Failed to generate barcode", foreground="red")
                    error_label.pack(padx=10, pady=10)
                    self.status_var.set("Failed to generate barcode")
            else:
                # If preview container is not available, just update the status
                if self.barcode_image:
                    self.status_var.set(f"Barcode generated: {barcode}")
                else:
                    self.status_var.set("Failed to generate barcode")

        except Exception as e:
            logger.error(f"Error generating barcode: {e}")
            messagebox.showerror("Error", f"Failed to generate barcode: {str(e)}")
            self.status_var.set("Error generating barcode")

    def save_barcode(self):
        """Save the generated barcode as an image file"""
        try:
            barcode = self.barcode_var.get().strip()
            if not barcode:
                raise ValueError("Please generate a barcode first")

            # Save barcode image
            filepath = save_barcode_image(barcode)
            if filepath:
                messagebox.showinfo("Success", f"Barcode saved to {filepath}")
            else:
                raise ValueError("Failed to save barcode image")

        except Exception as e:
            logger.error(f"Error saving barcode: {e}")
            messagebox.showerror("Error", f"Failed to save barcode: {str(e)}")

    def print_barcode_label(self):
        """Generate a printable PDF label with the barcode"""
        try:
            barcode = self.barcode_var.get().strip()
            if not barcode:
                messagebox.showwarning("No Barcode", "Please generate a barcode first")
                return

            # Get product information if available
            product_name = None
            price = None

            if 'name' in self.entries and self.entries['name'].get().strip():
                product_name = self.entries['name'].get().strip()

            if 'price' in self.entries and self.entries['price'].get().strip():
                try:
                    price = float(self.entries['price'].get().strip())
                except ValueError:
                    # If price is not a valid number, ignore it
                    pass

            # Show a message that we're generating the label
            self.status_var.set("Generating barcode label...")

            # Get the parent window for dialogs
            parent_window = self.parent
            if hasattr(self, 'root') and self.root is not None:
                parent_window = self.root

            # Update the UI
            parent_window.update()

            # Create a progress dialog
            progress_dialog = tk.Toplevel(parent_window)
            progress_dialog.title("Generating Label")
            progress_dialog.geometry("300x150")
            progress_dialog.transient(parent_window)
            progress_dialog.grab_set()

            # Center the dialog
            progress_dialog.update_idletasks()
            width = progress_dialog.winfo_width()
            height = progress_dialog.winfo_height()
            x = (progress_dialog.winfo_screenwidth() // 2) - (width // 2)
            y = (progress_dialog.winfo_screenheight() // 2) - (height // 2)
            progress_dialog.geometry(f"{width}x{height}+{x}+{y}")

            # Add a message and progress indicator
            ttk.Label(progress_dialog, text="Generating barcode label...",
                     font=("Arial", 12)).pack(pady=20)
            progress = ttk.Progressbar(progress_dialog, mode="indeterminate")
            progress.pack(fill="x", padx=20, pady=10)
            progress.start(10)

            # Update the UI
            parent_window.update()

            # Generate label PDF in a separate thread to keep UI responsive
            import threading
            from ..utils.label_generator import generate_barcode_label

            result = {"filepath": None, "error": None}

            def generate_label_thread():
                try:
                    result["filepath"] = generate_barcode_label(barcode, product_name, price)
                except Exception as e:
                    result["error"] = str(e)
                finally:
                    # Signal completion
                    progress_dialog.after(100, lambda: progress_dialog.destroy())

            # Start the thread
            thread = threading.Thread(target=generate_label_thread)
            thread.daemon = True
            thread.start()

            # Wait for the dialog to close (when thread completes)
            parent_window.wait_window(progress_dialog)

            # Process the result
            if result["error"]:
                self.status_var.set("Error generating barcode label")
                messagebox.showerror("Error", f"Failed to generate barcode label: {result['error']}")
                return

            filepath = result["filepath"]
            if filepath and os.path.exists(filepath):
                self.status_var.set("Barcode label generated successfully")

                # Show a preview dialog with the option to print
                preview_dialog = tk.Toplevel(parent_window)
                preview_dialog.title("Barcode Label Preview")
                preview_dialog.geometry("400x500")
                preview_dialog.transient(parent_window)
                preview_dialog.grab_set()

                # Center the dialog
                preview_dialog.update_idletasks()
                width = preview_dialog.winfo_width()
                height = preview_dialog.winfo_height()
                x = (preview_dialog.winfo_screenwidth() // 2) - (width // 2)
                y = (preview_dialog.winfo_screenheight() // 2) - (height // 2)
                preview_dialog.geometry(f"{width}x{height}+{x}+{y}")

                # Add a message
                ttk.Label(preview_dialog, text="Barcode Label Generated",
                         font=("Arial", 14, "bold")).pack(pady=10)

                # Create a frame for the PDF preview
                preview_frame = ttk.LabelFrame(preview_dialog, text="Label Preview")
                preview_frame.pack(fill="both", expand=True, padx=20, pady=10)

                # Try to create a PDF preview
                try:
                    # Use a simple message for now
                    preview_label = ttk.Label(preview_frame, text="PDF Preview Not Available\nClick 'Open PDF' to view the label",
                                            font=("Arial", 10))
                    preview_label.pack(pady=20, padx=20)

                    # Try to load the first page as an image if possible
                    try:
                        # First check if the file exists
                        if not os.path.exists(filepath):
                            logger.error(f"PDF file not found for preview: {filepath}")
                            raise FileNotFoundError(f"PDF file not found: {filepath}")

                        # Try different methods to preview the PDF
                        preview_success = False

                        # Method 1: Try using PyMuPDF (fitz)
                        try:
                            import sys
                            # Add potential PyMuPDF locations to path for PyInstaller
                            if getattr(sys, 'frozen', False):
                                base_path = os.path.dirname(sys.executable)
                                lib_paths = [
                                    os.path.join(base_path, 'lib'),
                                    os.path.join(base_path, 'PyMuPDF'),
                                    os.path.join(base_path, 'fitz')
                                ]
                                for lib_path in lib_paths:
                                    if os.path.exists(lib_path) and lib_path not in sys.path:
                                        sys.path.insert(0, lib_path)
                                        logger.debug(f"Added path for PDF preview: {lib_path}")

                            from PIL import Image, ImageTk
                            import fitz  # PyMuPDF

                            logger.debug(f"Opening PDF for preview: {filepath}")
                            doc = fitz.open(filepath)
                            if doc.page_count > 0:
                                # Get the first page
                                page = doc[0]
                                # Render page to an image
                                pix = page.get_pixmap(matrix=fitz.Matrix(1.5, 1.5))

                                # Convert to PIL Image
                                img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

                                # Convert to PhotoImage
                                tk_img = ImageTk.PhotoImage(image=img)

                                # Replace the text label with the image
                                preview_label.destroy()
                                img_label = ttk.Label(preview_frame, image=tk_img)
                                img_label.image = tk_img  # Keep a reference
                                img_label.pack(pady=10, padx=10)

                                logger.info("Successfully created PDF preview image using PyMuPDF")
                                preview_success = True
                        except ImportError as e:
                            logger.warning(f"PyMuPDF not available for PDF preview: {e}")
                        except Exception as e:
                            logger.warning(f"Error using PyMuPDF for preview: {e}")

                        # Method 2: If PyMuPDF failed, try using a simple image
                        if not preview_success:
                            try:
                                from PIL import Image, ImageTk

                                # Create a simple barcode image instead
                                barcode_img = generate_barcode_for_tk(barcode, width=300, height=100)
                                if barcode_img:
                                    # Replace the text label with the barcode image
                                    preview_label.destroy()
                                    img_label = ttk.Label(preview_frame, image=barcode_img)
                                    img_label.image = barcode_img  # Keep a reference
                                    img_label.pack(pady=10, padx=10)

                                    # Add text below the image
                                    if product_name:
                                        ttk.Label(preview_frame, text=product_name,
                                                font=("Arial", 10, "bold")).pack(pady=(5, 0))
                                    ttk.Label(preview_frame, text=barcode,
                                            font=("Courier", 10)).pack(pady=(2, 0))
                                    if price is not None:
                                        ttk.Label(preview_frame, text=f"${price:.2f}",
                                                font=("Arial", 10, "bold")).pack(pady=(2, 5))

                                    logger.info("Created simplified barcode preview image")
                                    preview_success = True
                            except Exception as e:
                                logger.warning(f"Error creating simplified preview: {e}")

                    except Exception as preview_error:
                        logger.warning(f"Could not create PDF preview: {preview_error}")
                except Exception as e:
                    logger.error(f"Error creating preview frame: {e}")

                # Show label details
                details_frame = ttk.Frame(preview_dialog)
                details_frame.pack(fill="x", padx=20, pady=10)

                ttk.Label(details_frame, text=f"Barcode: {barcode}",
                         font=("Arial", 10)).pack(anchor="w")
                if product_name:
                    ttk.Label(details_frame, text=f"Product: {product_name}",
                             font=("Arial", 10)).pack(anchor="w")
                if price is not None:
                    ttk.Label(details_frame, text=f"Price: ${price:.2f}",
                             font=("Arial", 10)).pack(anchor="w")

                ttk.Label(details_frame, text=f"File: {filepath}",
                         font=("Arial", 10)).pack(anchor="w", pady=(10, 0))

                # Add buttons
                button_frame = ttk.Frame(preview_dialog)
                button_frame.pack(fill="x", padx=20, pady=20)

                def open_pdf():
                    try:
                        os.startfile(filepath)
                        logger.info(f"Opened PDF file: {filepath}")
                    except Exception as open_error:
                        logger.error(f"Error opening PDF: {open_error}")
                        messagebox.showinfo("Note", f"The label was generated at {filepath} but could not be opened automatically.")

                def print_pdf():
                    try:
                        # First check if the file exists
                        if not os.path.exists(filepath):
                            raise FileNotFoundError(f"File not found: {filepath}")

                        # Try different methods to print the PDF
                        print_success = False

                        # Method 1: Try using os.startfile with 'print' operation (Windows only)
                        if os.name == 'nt':  # Windows
                            try:
                                logger.info(f"Attempting to print PDF using os.startfile: {filepath}")
                                os.startfile(filepath, "print")
                                logger.info("Print command sent successfully using os.startfile")
                                print_success = True

                                # Show confirmation message
                                messagebox.showinfo("Print Job Sent", "The label has been sent to the printer.")
                            except Exception as e:
                                logger.warning(f"Could not print using os.startfile: {e}")

                        # Method 2: Try using a cross-platform approach
                        if not print_success:
                            try:
                                import subprocess
                                import platform

                                system = platform.system()
                                logger.info(f"Attempting to print PDF using system commands on {system}")

                                if system == 'Windows':
                                    subprocess.Popen(['start', '', filepath], shell=True)
                                    logger.info("Opened PDF with default application on Windows")
                                elif system == 'Darwin':  # macOS
                                    subprocess.Popen(['open', filepath])
                                    logger.info("Opened PDF with default application on macOS")
                                else:  # Linux and others
                                    subprocess.Popen(['xdg-open', filepath])
                                    logger.info("Opened PDF with default application on Linux")

                                # Show instructions for manual printing
                                messagebox.showinfo("Print Instructions",
                                                  "The PDF has been opened. Please use the print function in your PDF viewer.")
                                print_success = True
                            except Exception as e:
                                logger.warning(f"Could not open PDF using system commands: {e}")

                        # Method 3: Last resort - just try to open the file
                        if not print_success:
                            try:
                                # Just try to open the file with the default application
                                os.startfile(filepath) if os.name == 'nt' else subprocess.Popen(['xdg-open', filepath])
                                logger.info("Opened PDF with default application as fallback")

                                # Show instructions for manual printing
                                messagebox.showinfo("Print Instructions",
                                                  "The PDF has been opened. Please use the print function in your PDF viewer.")
                                print_success = True
                            except Exception as e:
                                logger.error(f"Could not open PDF as fallback: {e}")

                                # Show error and file location
                                messagebox.showwarning("Print Error",
                                                     f"Could not print or open the PDF automatically.\n\n"
                                                     f"The PDF is saved at:\n{filepath}")
                    except Exception as print_error:
                        logger.error(f"Error in print_pdf function: {print_error}")

                        # Show error and alternative instructions
                        messagebox.showwarning("Print Error",
                                             f"Could not print automatically: {str(print_error)}\n\n"
                                             f"Please try opening the file and printing manually:\n{filepath}")

                # Add buttons with improved layout
                ttk.Button(button_frame, text="Open PDF", command=open_pdf).pack(side="left", padx=5)
                ttk.Button(button_frame, text="Print Label", command=print_pdf).pack(side="left", padx=5)
                ttk.Button(button_frame, text="Close", command=preview_dialog.destroy).pack(side="right", padx=5)
            else:
                self.status_var.set("Failed to generate barcode label")
                messagebox.showerror("Error", "Failed to generate barcode label. Please check the application logs for details.")

        except Exception as e:
            self.status_var.set("Error generating barcode label")
            logger.error(f"Error generating barcode label: {e}")
            messagebox.showerror("Error", f"Failed to generate barcode label: {str(e)}")

    def refresh_inventory(self, silent=False):
        """Refresh inventory list"""
        try:
            for item in self.tree.get_children():
                self.tree.delete(item)

            # No need to get threshold value anymore as we use min_stock from the database

            products = self.db.get_all_inventory()
            for product in products:
                # Store the product ID as a tag for later reference
                product_id = product[0]
                quantity = product[3] or 0
                min_stock = product[7] or 0

                # Create tag list and add low_stock tag if quantity is below or equal to min_stock
                tags = [str(product_id)]
                if min_stock > 0 and quantity <= min_stock:
                    tags.append('low_stock')

                # Columns: barcode, name, quantity, price, cost price, category, min stock, description
                self.tree.insert('', 'end', values=(
                    product[1],  # barcode (was product_code)
                    product[2],  # name
                    product[3],  # quantity
                    f"${product[4]:.2f}" if product[4] else "$0.00",  # unit_price
                    f"${product[5]:.2f}" if product[5] else "$0.00",  # cost_price
                    product[6],  # category
                    product[7],  # min_stock
                    product[8]   # description
                ), tags=tuple(tags))

        except Exception as e:
            logger.error(f"Failed to refresh inventory: {e}")
            if not silent:
                messagebox.showerror("Error", f"Failed to load inventory: {str(e)}")

    def on_inventory_changed(self, _=None):
        """Handle inventory changed event from other modules"""
        print("Inventory changed event received - refreshing inventory data...")
        self.refresh_inventory(silent=True)

    def refresh_data(self):
        """Auto-refresh the inventory data"""
        try:
            print("Auto-refreshing inventory data...")

            # Remember the currently selected item
            selected = self.tree.selection()
            selected_id = None
            if selected:
                item = self.tree.item(selected[0])
                if 'tags' in item and item['tags']:
                    selected_id = item['tags'][0]

            # Refresh the inventory
            self.refresh_inventory(silent=True)

            # Restore the selection if possible
            if selected_id:
                for item_id in self.tree.get_children():
                    item = self.tree.item(item_id)
                    if 'tags' in item and item['tags'] and item['tags'][0] == selected_id:
                        self.tree.selection_set(item_id)
                        self.tree.see(item_id)  # Ensure the item is visible
                        break

            print("Inventory data refreshed successfully")
        except Exception as e:
            print(f"Error during auto-refresh of inventory: {e}")
