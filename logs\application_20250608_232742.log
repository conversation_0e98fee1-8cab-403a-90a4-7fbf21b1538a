2025-06-08 23:27:42,097 - root - INFO - ===== TLT Group Management System Starting =====
2025-06-08 23:27:42,097 - root - INFO - Log file created at: C:\Users\<USER>\Desktop\music project\Accounting101\logs\application_20250608_232742.log
2025-06-08 23:27:42,145 - root - INFO - Searching for icon in: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-08 23:27:42,226 - root - INFO - Set icon using ICO file: C:\Users\<USER>\Desktop\music project\Accounting101\resources\TLT icon.ico
2025-06-08 23:27:42,274 - root - INFO - Found existing database at: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-08 23:27:42,276 - root - INFO - Selected database path: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-08 23:27:42,277 - root - INFO - Ensured database directory exists: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-08 23:27:42,292 - root - INFO - Database initialized successfully with WAL journal mode
2025-06-08 23:27:42,295 - root - INFO - Database check completed successfully
2025-06-08 23:27:42,300 - root - INFO - Loading module: Sales
2025-06-08 23:27:42,419 - root - INFO - Successfully loaded module: Sales
2025-06-08 23:27:42,420 - root - INFO - Loading module: Customers
2025-06-08 23:27:42,516 - root - INFO - Successfully loaded module: Customers
2025-06-08 23:27:42,517 - root - INFO - Loading module: Suppliers
2025-06-08 23:27:42,531 - modules.utils.table_customizer - INFO - Applied column order for suppliers: ['name', 'contact', 'address', 'balance']
2025-06-08 23:27:42,531 - modules.utils.table_customizer - INFO - Loaded saved column order for suppliers
2025-06-08 23:27:42,558 - root - INFO - Successfully loaded module: Suppliers
2025-06-08 23:27:42,559 - root - INFO - Loading module: Purchases
2025-06-08 23:27:42,620 - root - INFO - Successfully loaded module: Purchases
2025-06-08 23:27:42,621 - root - INFO - Loading module: Inventory
2025-06-08 23:27:42,671 - modules.utils.barcode_generator_new - INFO - Successfully imported PIL modules
2025-06-08 23:27:42,710 - modules.utils.barcode_generator_new - INFO - Successfully imported and tested barcode module
2025-06-08 23:27:42,878 - modules.utils.label_generator - INFO - Successfully imported reportlab modules
2025-06-08 23:27:42,932 - modules.utils.table_customizer - INFO - Applied column order for inventory: ['barcode', 'name', 'quantity', 'price', 'cost price', 'category', 'min stock', 'description']
2025-06-08 23:27:42,932 - modules.utils.table_customizer - INFO - Loaded saved column order for inventory
2025-06-08 23:27:42,935 - root - INFO - Successfully loaded module: Inventory
2025-06-08 23:27:42,935 - root - INFO - Loading module: Reports
2025-06-08 23:27:42,978 - root - INFO - Successfully loaded module: Reports
2025-06-08 23:27:42,978 - root - INFO - Loading module: Settings
2025-06-08 23:27:43,068 - root - INFO - Successfully loaded module: Settings
2025-06-08 23:27:43,069 - root - INFO - Application starting in unlocked state
2025-06-08 23:27:43,069 - root - INFO - Initializing security timer
2025-06-08 23:27:43,069 - root - INFO - Loaded security delay from config: 0 seconds
2025-06-08 23:27:43,069 - root - INFO - SecurityTimer initialized with delay: 0 seconds
2025-06-08 23:27:43,070 - root - INFO - Security timer initialized successfully
2025-06-08 23:27:43,071 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-08 23:27:46,340 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-08 23:27:46,341 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
