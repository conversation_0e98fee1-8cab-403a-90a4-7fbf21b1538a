;TLT Group Management System NSIS Installer Script
;Created for TLT Group SARL

;--------------------------------
;Include Modern UI
!include "MUI2.nsh"
!include "FileFunc.nsh"
!include "LogicLib.nsh"

;--------------------------------
;General Settings
Name "TLT Group Management System"
OutFile "TLT_Group_Management_System_Setup.exe"
Unicode True

;Default installation folder
InstallDir "$PROGRAMFILES64\TLT Group\Management System"

;Get installation folder from registry if available
InstallDirRegKey HKCU "Software\TLT Group\Management System" ""

;Request application privileges for Windows Vista/7/8/10/11
RequestExecutionLevel admin

;--------------------------------
;Variables
Var StartMenuFolder
Var PythonInstalled
Var PythonPath

;--------------------------------
;Interface Settings
!define MUI_ABORTWARNING
!define MUI_ICON "..\resources\TLT icon.ico"
!define MUI_UNICON "..\resources\TLT icon.ico"

;--------------------------------
;Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "license.txt"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY

;Start Menu Folder Page Configuration
!define MUI_STARTMENUPAGE_REGISTRY_ROOT "HKCU" 
!define MUI_STARTMENUPAGE_REGISTRY_KEY "Software\TLT Group\Management System" 
!define MUI_STARTMENUPAGE_REGISTRY_VALUENAME "Start Menu Folder"
!insertmacro MUI_PAGE_STARTMENU Application $StartMenuFolder

!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

;--------------------------------
;Languages
!insertmacro MUI_LANGUAGE "English"
!insertmacro MUI_LANGUAGE "French"

;--------------------------------
;Installer Sections

Section "TLT Group Management System (Required)" SecMain
  SectionIn RO
  
  ; Set output path to the installation directory
  SetOutPath $INSTDIR
  
  ; Copy main application files
  File "..\main.py"
  File "..\requirements.txt"
  File /nonfatal "..\README.md"
  File "run_app.bat"
  File "system_check.py"
  
  ; Copy modules directory
  SetOutPath "$INSTDIR\modules"
  File /r "..\modules\*.*"
  
  ; Copy resources directory
  SetOutPath "$INSTDIR\resources"
  File /r "..\resources\*.*"
  
  ; Copy config directory
  SetOutPath "$INSTDIR\config"
  File /r "..\config\*.*"
  
  ; Create logs directory
  SetOutPath "$INSTDIR\logs"
  File /nonfatal "..\logs\.gitkeep"
  
  ; Store installation folder
  WriteRegStr HKCU "Software\TLT Group\Management System" "" $INSTDIR
  
  ; Create uninstaller
  WriteUninstaller "$INSTDIR\Uninstall.exe"
  
  ; Add to Add/Remove Programs
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TLTGroupMS" \
                   "DisplayName" "TLT Group Management System"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TLTGroupMS" \
                   "UninstallString" "$INSTDIR\Uninstall.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TLTGroupMS" \
                   "InstallLocation" "$INSTDIR"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TLTGroupMS" \
                   "DisplayIcon" "$INSTDIR\resources\TLT icon.ico"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TLTGroupMS" \
                   "Publisher" "TLT Group SARL"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TLTGroupMS" \
                   "DisplayVersion" "1.0.0"
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TLTGroupMS" \
                     "NoModify" 1
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TLTGroupMS" \
                     "NoRepair" 1
  
  ; Get size of installation
  ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
  IntFmt $0 "0x%08X" $0
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TLTGroupMS" \
                     "EstimatedSize" "$0"

SectionEnd

Section "Desktop Shortcut" SecDesktop
  CreateShortcut "$DESKTOP\TLT Group Management System.lnk" \
                 "$INSTDIR\run_app.bat" \
                 "" \
                 "$INSTDIR\resources\TLT icon.ico" \
                 0 \
                 SW_SHOWNORMAL \
                 "" \
                 "TLT Group Management System"
SectionEnd

Section "Start Menu Shortcuts" SecStartMenu
  !insertmacro MUI_STARTMENU_WRITE_BEGIN Application
    
    CreateDirectory "$SMPROGRAMS\$StartMenuFolder"
    CreateShortcut "$SMPROGRAMS\$StartMenuFolder\TLT Group Management System.lnk" \
                   "$INSTDIR\run_app.bat" \
                   "" \
                   "$INSTDIR\resources\TLT icon.ico" \
                   0 \
                   SW_SHOWNORMAL \
                   "" \
                   "TLT Group Management System"
    
    CreateShortcut "$SMPROGRAMS\$StartMenuFolder\System Check.lnk" \
                   "python.exe" \
                   '"$INSTDIR\system_check.py"' \
                   "$INSTDIR\resources\TLT icon.ico" \
                   0 \
                   SW_SHOWNORMAL \
                   "$INSTDIR" \
                   "Check System Requirements"
    
    CreateShortcut "$SMPROGRAMS\$StartMenuFolder\Uninstall.lnk" \
                   "$INSTDIR\Uninstall.exe"
  
  !insertmacro MUI_STARTMENU_WRITE_END
SectionEnd

Section "Python Dependencies" SecPython
  DetailPrint "Checking Python installation..."
  
  ; Check if Python is installed
  nsExec::ExecToStack 'python --version'
  Pop $0
  ${If} $0 == 0
    DetailPrint "Python found, installing dependencies..."
    nsExec::ExecToLog 'python -m pip install -r "$INSTDIR\requirements.txt"'
    Pop $0
    ${If} $0 == 0
      DetailPrint "Dependencies installed successfully"
    ${Else}
      DetailPrint "Warning: Some dependencies may not have installed correctly"
    ${EndIf}
  ${Else}
    DetailPrint "Python not found. Please install Python 3.8+ manually."
    MessageBox MB_ICONEXCLAMATION "Python not found!$\n$\nPlease install Python 3.8 or higher from https://python.org$\n$\nMake sure to check 'Add Python to PATH' during installation."
  ${EndIf}
SectionEnd

;--------------------------------
;Descriptions

;Language strings
LangString DESC_SecMain ${LANG_ENGLISH} "Main application files (required)"
LangString DESC_SecDesktop ${LANG_ENGLISH} "Create a desktop shortcut"
LangString DESC_SecStartMenu ${LANG_ENGLISH} "Create Start Menu shortcuts"
LangString DESC_SecPython ${LANG_ENGLISH} "Install Python dependencies automatically"

LangString DESC_SecMain ${LANG_FRENCH} "Fichiers principaux de l'application (requis)"
LangString DESC_SecDesktop ${LANG_FRENCH} "Créer un raccourci sur le bureau"
LangString DESC_SecStartMenu ${LANG_FRENCH} "Créer des raccourcis dans le menu Démarrer"
LangString DESC_SecPython ${LANG_FRENCH} "Installer automatiquement les dépendances Python"

;Assign language strings to sections
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SecMain} $(DESC_SecMain)
  !insertmacro MUI_DESCRIPTION_TEXT ${SecDesktop} $(DESC_SecDesktop)
  !insertmacro MUI_DESCRIPTION_TEXT ${SecStartMenu} $(DESC_SecStartMenu)
  !insertmacro MUI_DESCRIPTION_TEXT ${SecPython} $(DESC_SecPython)
!insertmacro MUI_FUNCTION_DESCRIPTION_END

;--------------------------------
;Uninstaller Section

Section "Uninstall"

  ; Remove registry keys
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TLTGroupMS"
  DeleteRegKey HKCU "Software\TLT Group\Management System"

  ; Remove files and uninstaller
  Delete "$INSTDIR\main.py"
  Delete "$INSTDIR\requirements.txt"
  Delete "$INSTDIR\README.md"
  Delete "$INSTDIR\run_app.bat"
  Delete "$INSTDIR\system_check.py"
  Delete "$INSTDIR\Uninstall.exe"
  
  ; Remove directories
  RMDir /r "$INSTDIR\modules"
  RMDir /r "$INSTDIR\resources"
  RMDir /r "$INSTDIR\config"
  RMDir /r "$INSTDIR\logs"
  RMDir "$INSTDIR"

  ; Remove shortcuts
  Delete "$DESKTOP\TLT Group Management System.lnk"
  
  !insertmacro MUI_STARTMENU_GETFOLDER Application $StartMenuFolder
  Delete "$SMPROGRAMS\$StartMenuFolder\TLT Group Management System.lnk"
  Delete "$SMPROGRAMS\$StartMenuFolder\System Check.lnk"
  Delete "$SMPROGRAMS\$StartMenuFolder\Uninstall.lnk"
  RMDir "$SMPROGRAMS\$StartMenuFolder"

SectionEnd

;--------------------------------
;Installer Functions

Function .onInit
  ; Check if already installed
  ReadRegStr $R0 HKCU "Software\TLT Group\Management System" ""
  ${If} $R0 != ""
    MessageBox MB_YESNO|MB_ICONQUESTION \
      "TLT Group Management System is already installed at:$\n$\n$R0$\n$\nDo you want to continue and overwrite the existing installation?" \
      /SD IDYES IDYES +2
    Abort
  ${EndIf}
  
  ; Set default sections
  SectionSetFlags ${SecMain} 17        ; Required + Selected
  SectionSetFlags ${SecDesktop} 1      ; Selected
  SectionSetFlags ${SecStartMenu} 1    ; Selected
  SectionSetFlags ${SecPython} 1       ; Selected
FunctionEnd

Function .onInstSuccess
  MessageBox MB_YESNO|MB_ICONQUESTION \
    "Installation completed successfully!$\n$\nWould you like to run the TLT Group Management System now?" \
    /SD IDNO IDNO +2
  ExecShell "" "$INSTDIR\run_app.bat"
FunctionEnd

;--------------------------------
;Uninstaller Functions

Function un.onInit
  MessageBox MB_YESNO|MB_ICONQUESTION \
    "Are you sure you want to completely remove TLT Group Management System and all of its components?" \
    /SD IDYES IDYES +2
  Abort
FunctionEnd

Function un.onUninstSuccess
  MessageBox MB_ICONINFORMATION \
    "TLT Group Management System has been successfully removed from your computer.$\n$\nNote: Your invoice data in Documents\TLT_Invoices has been preserved."
FunctionEnd
