2025-06-08 23:34:46,389 - root - INFO - ===== TLT Group Management System Starting =====
2025-06-08 23:34:46,389 - root - INFO - Log file created at: C:\Users\<USER>\Desktop\music project\Accounting101\logs\application_20250608_233446.log
2025-06-08 23:34:46,442 - root - INFO - Searching for icon in: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-08 23:34:46,525 - root - INFO - Set icon using ICO file: C:\Users\<USER>\Desktop\music project\Accounting101\resources\TLT icon.ico
2025-06-08 23:34:46,574 - root - INFO - Found existing database at: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-08 23:34:46,581 - root - INFO - Selected database path: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-08 23:34:46,583 - root - INFO - Ensured database directory exists: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-08 23:34:46,597 - root - INFO - Database initialized successfully with WAL journal mode
2025-06-08 23:34:46,600 - root - INFO - Database check completed successfully
2025-06-08 23:34:46,602 - root - INFO - Loading module: Sales
2025-06-08 23:34:46,719 - root - INFO - Successfully loaded module: Sales
2025-06-08 23:34:46,719 - root - INFO - Loading module: Customers
2025-06-08 23:34:46,805 - root - INFO - Successfully loaded module: Customers
2025-06-08 23:34:46,805 - root - INFO - Loading module: Suppliers
2025-06-08 23:34:46,821 - modules.utils.table_customizer - INFO - Applied column order for suppliers: ['name', 'contact', 'address', 'balance']
2025-06-08 23:34:46,821 - modules.utils.table_customizer - INFO - Loaded saved column order for suppliers
2025-06-08 23:34:46,851 - root - INFO - Successfully loaded module: Suppliers
2025-06-08 23:34:46,851 - root - INFO - Loading module: Purchases
2025-06-08 23:34:46,902 - root - INFO - Successfully loaded module: Purchases
2025-06-08 23:34:46,902 - root - INFO - Loading module: Inventory
2025-06-08 23:34:46,960 - modules.utils.barcode_generator_new - INFO - Successfully imported PIL modules
2025-06-08 23:34:47,002 - modules.utils.barcode_generator_new - INFO - Successfully imported and tested barcode module
2025-06-08 23:34:47,168 - modules.utils.label_generator - INFO - Successfully imported reportlab modules
2025-06-08 23:34:47,245 - modules.utils.table_customizer - INFO - Applied column order for inventory: ['barcode', 'name', 'quantity', 'price', 'cost price', 'category', 'min stock', 'description']
2025-06-08 23:34:47,246 - modules.utils.table_customizer - INFO - Loaded saved column order for inventory
2025-06-08 23:34:47,249 - root - INFO - Successfully loaded module: Inventory
2025-06-08 23:34:47,249 - root - INFO - Loading module: Reports
2025-06-08 23:34:47,289 - root - INFO - Successfully loaded module: Reports
2025-06-08 23:34:47,290 - root - INFO - Loading module: Settings
2025-06-08 23:34:47,380 - root - INFO - Successfully loaded module: Settings
2025-06-08 23:34:47,381 - root - INFO - Application starting in unlocked state
2025-06-08 23:34:47,381 - root - INFO - Initializing security timer
2025-06-08 23:34:47,382 - root - INFO - Loaded security delay from config: 0 seconds
2025-06-08 23:34:47,382 - root - INFO - SecurityTimer initialized with delay: 0 seconds
2025-06-08 23:34:47,382 - root - INFO - Security timer initialized successfully
2025-06-08 23:34:47,383 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-08 23:34:50,831 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-08 23:34:50,831 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
2025-06-08 23:35:20,666 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-08 23:35:37,224 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-08 23:35:37,225 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
2025-06-08 23:36:03,891 - root - INFO - Application closing - performing cleanup
2025-06-08 23:36:03,939 - root - INFO - ===== TLT Group Management System Shutting Down =====
