#!/bin/bash

echo "========================================"
echo "Building TLT Group Management System Installer"
echo "Using NSIS (Nullsoft Scriptable Install System)"
echo "========================================"
echo

# Check if we're in MSYS2 environment
if [[ "$MSYSTEM" == "" ]]; then
    echo "⚠️  This script is optimized for MSYS2 environment"
    echo "   You can still run it, but paths may need adjustment"
    echo
fi

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check for NSIS
NSIS_PATHS=(
    "/mingw64/bin/makensis.exe"
    "/usr/bin/makensis.exe"
    "/c/Program Files (x86)/NSIS/makensis.exe"
    "/c/Program Files/NSIS/makensis.exe"
    "/c/NSIS/makensis.exe"
)

NSIS_PATH=""
for path in "${NSIS_PATHS[@]}"; do
    if [[ -f "$path" ]]; then
        NSIS_PATH="$path"
        break
    fi
done

if [[ "$NSIS_PATH" == "" ]]; then
    echo "❌ NSIS not found in any of these locations:"
    for path in "${NSIS_PATHS[@]}"; do
        echo "   - $path"
    done
    echo
    echo "📥 Installation options:"
    echo "   1. For MSYS2: pacman -S mingw-w64-x86_64-nsis"
    echo "   2. Download from: https://nsis.sourceforge.io/Download"
    echo
    exit 1
fi

echo "✅ Found NSIS at: $NSIS_PATH"
echo

# Check required files
echo "🔍 Checking required files..."
MISSING_FILES=0

check_file() {
    if [[ ! -f "$1" ]]; then
        echo "❌ Missing: $1"
        MISSING_FILES=1
    else
        echo "✅ Found: $1"
    fi
}

check_file "../main.py"
check_file "../requirements.txt"
check_file "../resources/TLT icon.ico"
check_file "license.txt"
check_file "run_app.bat"
check_file "system_check.py"
check_file "tlt_installer.nsi"

if [[ $MISSING_FILES -eq 1 ]]; then
    echo
    echo "❌ Some required files are missing. Please ensure all files are present."
    exit 1
fi

echo
echo "✅ All required files found"
echo

# Create output directory
mkdir -p output

# Clean previous builds
rm -f output/*.exe
rm -f TLT_Group_Management_System_Setup.exe

echo "🔨 Compiling installer with NSIS..."
echo

# Convert paths for Windows if needed
if [[ "$MSYSTEM" != "" ]]; then
    # We're in MSYS2, convert the NSI file path
    NSI_FILE="$(cygpath -w "$(pwd)/tlt_installer.nsi")"
else
    NSI_FILE="tlt_installer.nsi"
fi

# Compile the installer
"$NSIS_PATH" /V3 "$NSI_FILE"
BUILD_RESULT=$?

echo

if [[ $BUILD_RESULT -eq 0 ]]; then
    echo "========================================"
    echo "✅ Installer built successfully!"
    echo "========================================"
    echo
    
    # Check if the output file exists
    if [[ -f "TLT_Group_Management_System_Setup.exe" ]]; then
        # Move to output directory
        mv "TLT_Group_Management_System_Setup.exe" "output/"
        
        # Get file size
        FILE_SIZE=$(stat -c%s "output/TLT_Group_Management_System_Setup.exe" 2>/dev/null || stat -f%z "output/TLT_Group_Management_System_Setup.exe" 2>/dev/null)
        SIZE_MB=$((FILE_SIZE / 1048576))
        
        echo "📁 Output file: output/TLT_Group_Management_System_Setup.exe"
        echo "📏 File size: ${SIZE_MB} MB"
        echo
        echo "📋 Installer Features:"
        echo "   ✅ Professional installation wizard"
        echo "   ✅ Automatic Python dependency installation"
        echo "   ✅ Desktop and Start Menu shortcuts"
        echo "   ✅ System requirements checking"
        echo "   ✅ Clean uninstallation"
        echo "   ✅ Multi-language support (English/French)"
        echo "   ✅ Add/Remove Programs integration"
        echo
        echo "🚀 Ready for distribution!"
        echo
        
        # Open output folder in Windows Explorer if available
        if command_exists explorer.exe; then
            explorer.exe "$(cygpath -w "$(pwd)/output")" 2>/dev/null &
        elif command_exists xdg-open; then
            xdg-open "output" 2>/dev/null &
        fi
        
        echo "📦 Distribution Instructions:"
        echo "   1. Test the installer on a clean Windows system"
        echo "   2. Distribute the .exe file to end users"
        echo "   3. Users should run as Administrator for best results"
        echo
        
    else
        echo "❌ Output file not found. Build may have failed."
        exit 1
    fi
else
    echo "========================================"
    echo "❌ Build failed!"
    echo "========================================"
    echo
    echo "🔧 Common issues and solutions:"
    echo "   - Missing source files → Check file paths"
    echo "   - NSIS syntax errors → Review tlt_installer.nsi"
    echo "   - Permission issues → Run with appropriate permissions"
    echo "   - Path issues → Ensure all paths use forward slashes in NSI"
    echo
    exit 1
fi

echo "✨ Build process completed successfully!"
echo
read -p "Press Enter to continue..."
