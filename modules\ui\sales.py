from .base_module import BaseModule
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime
import json
import os

class SalesModule(BaseModule):
    def __init__(self, parent, db):
        super().__init__(parent, db)
        self.init_variables()
        self.create_sales_interface()
        self.refresh_customers()
        self.refresh_products()

        # Set up auto-refresh
        self.refresh_interval = 60000  # 60 seconds
        self.setup_auto_refresh()

        # Bind to settings changed event
        self.parent.bind('<<SettingsChanged>>', self.on_settings_changed)

    def init_variables(self):
        """Initialize all variables before use"""
        self.customer_id = tk.StringVar()
        self.barcode_var = tk.StringVar()
        self.quantity_var = tk.StringVar(value="1")
        self.total_var = tk.StringVar(value="0.00")
        self.subtotal_var = tk.StringVar(value="0.00")
        self.discount_amount = tk.StringVar(value="0")
        self.discount_type = tk.StringVar(value="amount")  # 'amount' or 'percent'
        self.deposit_amount = tk.StringVar(value="0")  # New deposit amount variable

        # Track the current invoice ID to allow updating instead of creating new invoices
        self.current_invoice_id = None

        # Load settings from database
        try:
            self.db.cursor.execute("SELECT value FROM settings WHERE key = 'tax_rate'")
            result = self.db.cursor.fetchone()
            vat_value = float(result[0]) if result else 15.0
            self.vat_rate = tk.StringVar(value=str(vat_value))
        except Exception as e:
            print(f"Debug - Error loading VAT: {e}")
            self.vat_rate = tk.StringVar(value="15.0")

        self.customer_cb = None
        self.product_cb = None
        self.barcode_entry = None
        self.products_tree = None

    def refresh_vat_rate(self):
        """Update VAT rate from settings"""
        try:
            # First try to get from database
            self.db.cursor.execute("SELECT value FROM settings WHERE key = 'tax_rate'")
            result = self.db.cursor.fetchone()
            if result:
                self.vat_rate.set(str(float(result[0])))
                print(f"Debug - Loaded VAT rate from database: {result[0]}")
                return

            # If not in database, try config file
            with open('config/config.json', 'r') as f:
                settings = json.load(f)
                self.vat_rate.set(str(settings.get('tax_rate', 15.0)))
                print(f"Debug - Loaded VAT rate from config file: {settings.get('tax_rate', 15.0)}")
        except Exception as e:
            print(f"Error loading VAT rate: {e}")
            self.vat_rate.set("15.0")

    def on_settings_changed(self, event=None):
        """Handle settings changed event"""
        print("Debug - Settings changed event received, refreshing VAT rate")
        self.refresh_vat_rate()
        # Update the total to reflect the new VAT rate
        self.update_total()

    def create_sales_interface(self):
        # Invoice entry form
        self.invoice_frame = ttk.LabelFrame(self.frame, text="New Invoice")
        self.invoice_frame.pack(fill="x", padx=5, pady=5)

        # Customer selection
        ttk.Label(self.invoice_frame, text="Customer:").grid(row=0, column=0, padx=5, pady=5)
        self.customer_id = tk.StringVar()
        self.customer_cb = ttk.Combobox(self.invoice_frame, textvariable=self.customer_id, width=40)
        self.customer_cb.grid(row=0, column=1, padx=5, pady=5)

        ttk.Button(self.invoice_frame, text="Refresh",
                  command=self.refresh_customers).grid(row=0, column=2, padx=5)

        # Barcode scanning section
        ttk.Label(self.invoice_frame, text="Barcode:").grid(row=1, column=0, padx=5, pady=5)
        self.barcode_var = tk.StringVar()
        self.barcode_entry = ttk.Entry(self.invoice_frame, textvariable=self.barcode_var, width=40)
        self.barcode_entry.grid(row=1, column=1, padx=5, pady=5)
        self.barcode_entry.bind('<Return>', self.scan_barcode)

        ttk.Button(self.invoice_frame, text="Add",
                  command=self.scan_barcode).grid(row=1, column=2, padx=5)

        # Add more invoice entry fields and product table here

        # Add product selection
        product_frame = ttk.LabelFrame(self.frame, text="Add Product")
        product_frame.pack(fill="x", padx=5, pady=5)

        # Regular product selection
        ttk.Label(product_frame, text="Product:").grid(row=0, column=0, padx=5)
        self.product_cb = ttk.Combobox(product_frame, width=50)
        self.product_cb.grid(row=0, column=1, padx=5, sticky='ew')

        ttk.Label(product_frame, text="Quantity:").grid(row=0, column=2, padx=5)
        self.quantity_var = tk.StringVar(value="1")
        self.quantity_entry = ttk.Entry(product_frame, textvariable=self.quantity_var, width=10)
        self.quantity_entry.grid(row=0, column=3, padx=5)

        # Custom item section
        custom_frame = ttk.LabelFrame(self.frame, text="Custom Item")
        custom_frame.pack(fill="x", padx=5, pady=5)

        # First row - Name and Quantity
        ttk.Label(custom_frame, text="Name:").grid(row=0, column=0, padx=5, pady=2)
        self.custom_name_var = tk.StringVar()
        ttk.Entry(custom_frame, textvariable=self.custom_name_var, width=50).grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(custom_frame, text="Quantity:").grid(row=0, column=2, padx=5, pady=2)
        self.custom_qty_var = tk.StringVar(value="1")
        ttk.Entry(custom_frame, textvariable=self.custom_qty_var, width=10).grid(row=0, column=3, padx=5, pady=2)

        ttk.Label(custom_frame, text="Price:").grid(row=0, column=4, padx=5, pady=2)
        self.custom_price_var = tk.StringVar(value="0.00")
        ttk.Entry(custom_frame, textvariable=self.custom_price_var, width=10).grid(row=0, column=5, padx=5, pady=2)

        # Second row - Description
        ttk.Label(custom_frame, text="Description:").grid(row=1, column=0, padx=5, pady=2)
        self.custom_desc_var = tk.StringVar()
        ttk.Entry(custom_frame, textvariable=self.custom_desc_var, width=50).grid(row=1, column=1, columnspan=4, sticky='ew', padx=5, pady=2)

        # Add button in second row
        ttk.Button(custom_frame, text="Add Custom Item",
                  command=self.add_custom_item).grid(row=1, column=5, padx=5, pady=2)

        ttk.Button(product_frame, text="Add Item", command=self.add_item).grid(row=0, column=4, padx=5)

        # Products table
        self.products_frame = ttk.LabelFrame(self.frame, text="Products")
        self.products_frame.pack(fill="both", expand=True, padx=5, pady=5)

        columns = ('barcode', 'name', 'description', 'quantity', 'price', 'total')  # Added 'total' back
        self.products_tree = ttk.Treeview(self.products_frame, columns=columns, show='headings')

        # Configure column headings
        for col in columns:
            self.products_tree.heading(col, text=col.title())

        # Configure column widths
        self.products_tree.column('barcode', width=120, minwidth=100)
        self.products_tree.column('name', width=200, minwidth=150)
        self.products_tree.column('description', width=200, minwidth=150)
        self.products_tree.column('quantity', width=80, minwidth=60)
        self.products_tree.column('price', width=80, minwidth=60)
        self.products_tree.column('total', width=80, minwidth=60)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(self.products_frame, orient="vertical", command=self.products_tree.yview)
        scrollbar.pack(side="right", fill="y")
        self.products_tree.configure(yscrollcommand=scrollbar.set)

        self.products_tree.pack(fill="both", expand=True, side="left")

        # Add right-click menu for item management
        self.item_menu = tk.Menu(self.products_tree, tearoff=0)
        self.item_menu.add_command(label="Increase Quantity", command=self.increase_quantity)
        self.item_menu.add_command(label="Decrease Quantity", command=self.decrease_quantity)
        self.item_menu.add_separator()
        self.item_menu.add_command(label="Remove Item", command=self.remove_item)

        # Bind right-click to show menu
        self.products_tree.bind("<Button-3>", self.show_item_menu)

        # Bind double-click to edit quantity
        self.products_tree.bind("<Double-1>", self.edit_quantity)

        # Create custom styles for buttons
        style = ttk.Style()
        style.configure('Print.TButton',
                       font=('Helvetica', 12, 'bold'),
                       padding=10,
                       background='#90EE90')

        # Totals frame
        totals_frame = ttk.Frame(self.frame)
        totals_frame.pack(fill="x", padx=5, pady=5)

        # Left side: Subtotal and VAT info
        left_frame = ttk.Frame(totals_frame)
        left_frame.pack(side="left", fill="x", expand=True)

        ttk.Label(left_frame, text="Subtotal:").pack(side="left")
        ttk.Label(left_frame, textvariable=self.subtotal_var).pack(side="left", padx=(5,20))

        ttk.Label(left_frame, text="VAT (%):").pack(side="left")
        ttk.Label(left_frame, textvariable=self.vat_rate).pack(side="left", padx=5)

        # Right side: Total
        right_frame = ttk.Frame(totals_frame)
        right_frame.pack(side="right")

        ttk.Label(right_frame, text="Total (with VAT):").pack(side="left", padx=(20,5))
        ttk.Label(right_frame, textvariable=self.total_var).pack(side="left", padx=(0,20))

        # Bottom frame for buttons
        button_frame = ttk.Frame(self.frame)
        button_frame.pack(fill="x", padx=5, pady=5)

        # Create custom style for Save button
        style = ttk.Style()
        style.configure('Save.TButton',
                       font=('Helvetica', 10, 'bold'),
                       padding=5,
                       background='#ADD8E6')  # Light blue background

        # Save Invoice button with custom style
        self.save_button = ttk.Button(button_frame, text="Save Invoice",
                                    command=self.save_invoice,
                                    style='Save.TButton')
        self.save_button.pack(side="left", pady=5)

        # Add a label to show when editing an existing invoice
        self.edit_mode_label = ttk.Label(button_frame, text="", foreground="blue")
        self.edit_mode_label.pack(side="left", padx=5, pady=5)
        ttk.Button(button_frame, text="Clear",
                  command=self.clear_form).pack(side="left", padx=5, pady=5)
        ttk.Button(button_frame, text="Refresh Products",
                  command=self.refresh_products).pack(side="left", padx=5, pady=5)
        ttk.Button(button_frame, text="Print as Price List",
                  command=self.print_as_price_list).pack(side="left", padx=5, pady=5)

        # Discount and deposit frame on the right
        discount_frame = ttk.Frame(button_frame)
        discount_frame.pack(side="right", padx=5, pady=5)

        # Discount type radio buttons
        discount_type_frame = ttk.Frame(discount_frame)
        discount_type_frame.pack(side="top", fill="x")

        ttk.Radiobutton(discount_type_frame, text="$", variable=self.discount_type,
                       value="amount", command=self.update_total).pack(side="left")
        ttk.Radiobutton(discount_type_frame, text="%", variable=self.discount_type,
                       value="percent", command=self.update_total).pack(side="left")

        # Discount amount entry
        discount_entry_frame = ttk.Frame(discount_frame)
        discount_entry_frame.pack(side="top", fill="x", pady=2)

        ttk.Label(discount_entry_frame, text="Discount:").pack(side="left")
        discount_entry = ttk.Entry(discount_entry_frame, textvariable=self.discount_amount, width=8)
        discount_entry.pack(side="left", padx=2)
        discount_entry.bind("<KeyRelease>", lambda _: self.update_total())

        # Deposit amount entry
        deposit_entry_frame = ttk.Frame(discount_frame)
        deposit_entry_frame.pack(side="top", fill="x", pady=2)

        ttk.Label(deposit_entry_frame, text="Deposit:  ").pack(side="left")
        deposit_entry = ttk.Entry(deposit_entry_frame, textvariable=self.deposit_amount, width=8)
        deposit_entry.pack(side="left", padx=2)
        deposit_entry.bind("<KeyRelease>", lambda _: self.update_total())

        # Deposit date entry
        deposit_date_frame = ttk.Frame(discount_frame)
        deposit_date_frame.pack(side="top", fill="x", pady=2)

        ttk.Label(deposit_date_frame, text="Deposit Date:").pack(side="left")
        self.deposit_date = tk.StringVar()
        deposit_date_entry = ttk.Entry(deposit_date_frame, textvariable=self.deposit_date, width=12)
        deposit_date_entry.pack(side="left", padx=2)

        # Add a button to set today's date
        today_button = ttk.Button(deposit_date_frame, text="Today",
                                 command=lambda: self.deposit_date.set(datetime.now().strftime('%Y-%m-%d')),
                                 width=6)
        today_button.pack(side="left", padx=2)

        # Print button with custom style
        print_button = ttk.Button(button_frame, text="Print Invoice",
                                command=self.print_invoice,
                                style='Print.TButton')
        print_button.pack(side="right", padx=5, pady=5)

        self.refresh_vat_rate()  # Add this line to refresh VAT rate on startup

    def refresh_customers(self, silent=False):
        """Update customer dropdown list"""
        try:
            customers = self.db.get_customer_names()
            # Store customer data with IDs as tags in a dictionary for later lookup
            self.customer_data = {name: id for id, name in customers}
            # Display only names in the dropdown
            self.customer_cb['values'] = list(self.customer_data.keys())
        except Exception as e:
            print(f"Error refreshing customers: {e}")
            if not silent:
                messagebox.showerror("Error", f"Failed to refresh customers: {str(e)}")

    def refresh_products(self, silent=False):
        """Refresh products list from inventory"""
        try:
            # Get all products with stock from database
            products = self.db.get_all_products_for_sale()
            print(f"Debug - Products for sale returned from DB: {len(products)}")

            product_list = []
            for product in products:
                # product format: (id, name, quantity, unit_price)
                print(f"Debug - Product: ID={product[0]}, Name={product[1]}, Qty={product[2]}, Price={product[3]}")
                # Removed the redundant check since get_all_products_for_sale already filters for quantity > 0
                product_str = f"{product[1]} (Stock: {product[2]}, Price: ${product[3]:.2f})"
                product_list.append(product_str)

            if product_list:
                self.product_cb['values'] = product_list
                print(f"Debug - Added {len(product_list)} products to dropdown")
            else:
                self.product_cb['values'] = ['No products available']
                print("Debug - No products available for sale")
            self.product_cb.set('')

        except Exception as e:
            print(f"Debug - Products refresh error: {str(e)}")
            if not silent:
                messagebox.showerror("Error", f"Failed to load products: {str(e)}")

    def refresh_data(self):
        """Auto-refresh the sales data"""
        try:
            print("Auto-refreshing sales data...")

            # Refresh customers
            self.refresh_customers(silent=True)

            # Refresh products
            self.refresh_products(silent=True)

            print("Sales data refreshed successfully")
        except Exception as e:
            print(f"Error during auto-refresh of sales: {e}")

    def validate_stock_for_invoice(self, items):
        """Validate that all items have sufficient stock before processing"""
        for item in items:
            # Get product by name
            self.db.cursor.execute(
                "SELECT id, quantity FROM inventory WHERE name = ?",
                (item['name'],)
            )
            result = self.db.cursor.fetchone()

            # Skip custom items (not in inventory)
            if not result:
                print(f"Product '{item['name']}' not found in inventory - treating as custom item")
                continue

            _, available = result  # We only need the available quantity

            # Check if we have enough stock for this item
            if available < item['quantity']:
                raise ValueError(f"Insufficient stock for {item['name']}. Requested: {item['quantity']}, Available: {available}")

        return True

    def save_invoice(self, print_after=False):
        try:
            customer_id_or_name = self.customer_id.get()
            if not customer_id_or_name:
                raise ValueError("Please select a customer")

            # Check if the customer_id is a numeric ID or a name
            try:
                # Try to convert to integer - if successful, it's an ID
                customer_id = int(customer_id_or_name)
                # Verify that this customer ID exists
                customer = self.db.get_customer_by_id(customer_id)
                if not customer:
                    raise ValueError(f"Customer with ID '{customer_id}' not found in database")
                customer_name = customer[1]  # Get the name for logging
            except ValueError:
                # Not an integer, so treat as a name
                customer_name = customer_id_or_name
                # Look up customer ID from the name using our dictionary
                if customer_name not in self.customer_data:
                    raise ValueError(f"Customer '{customer_name}' not found in database")
                customer_id = self.customer_data[customer_name]

            print(f"Debug - Saving invoice for customer: {customer_name} (ID: {customer_id})")

            items = self.get_invoice_items()
            print(f"Debug - Items to save: {items}")

            if not items:
                raise ValueError("No items added to invoice")

            # Validate stock availability for all items
            self.validate_stock_for_invoice(items)

            # Get values for invoice
            subtotal = float(str(self.subtotal_var.get()).replace('$', '').replace(',', ''))
            total = float(str(self.total_var.get()).replace('$', '').replace(',', ''))
            discount_value = float(self.discount_amount.get() or 0)
            discount_type = self.discount_type.get()
            deposit_value = float(self.deposit_amount.get() or 0)
            deposit_date_value = self.deposit_date.get().strip() if self.deposit_date.get().strip() else None
            vat_rate = float(self.vat_rate.get() or 0)

            print(f"Debug - Invoice subtotal: {subtotal}, total: {total}, discount: {discount_value} ({discount_type}), deposit: {deposit_value}, deposit_date: {deposit_date_value}")

            # Check if we're updating an existing invoice or creating a new one
            if self.current_invoice_id:
                # Update existing invoice
                print(f"Debug - Updating existing invoice #{self.current_invoice_id}")
                invoice_id = self.db.update_invoice(
                    invoice_id=self.current_invoice_id,
                    customer_id=customer_id,
                    items=items,
                    total=total,
                    vat_rate=vat_rate,
                    discount_amount=discount_value,
                    discount_type=discount_type,
                    deposit_amount=deposit_value,
                    deposit_date=deposit_date_value
                )
                action_text = "updated"
            else:
                # Create new invoice
                print(f"Debug - Creating new invoice")
                invoice_id = self.db.process_sale(
                    customer_id=customer_id,
                    items=items,
                    total=total,
                    vat_rate=vat_rate,
                    discount_amount=discount_value,
                    discount_type=discount_type,
                    deposit_amount=deposit_value,
                    deposit_date=deposit_date_value
                )
                action_text = "saved"

            # Store the current invoice ID for future updates
            self.current_invoice_id = invoice_id

            print(f"Debug - Invoice {action_text} with ID: {invoice_id}")

            # Set invoice status based on whether we're printing or just saving
            if print_after:
                # If printing, it will be marked as Paid later in the print_invoice method
                pass
            else:
                # When just saving, always mark as Not Paid
                self.db.update_invoice_status(invoice_id, 'Not Paid')
                print(f"Debug - Invoice status set to Not Paid")

                # Don't clear the form after saving - keep the invoice visible
                # Just show success message
                messagebox.showinfo("Success", f"Invoice #{invoice_id} {action_text} successfully!")

            # Notify any open inventory module to refresh
            self.parent.event_generate('<<InventoryChanged>>', when='tail')

            return invoice_id

        except Exception as e:
            print(f"Debug - Error saving invoice: {e}")
            messagebox.showerror("Error", f"Failed to save invoice: {str(e)}")
            return None

    def generate_invoice_pdf(self, invoice_id, customer_id, items, price_list_mode=False):
        """Generate PDF invoice or price list

        Args:
            invoice_id: ID of the invoice (0 for price list mode)
            customer_id: ID of the customer
            items: List of items to include
            price_list_mode: If True, generate a price list instead of an invoice
        """
        try:
            print(f"Debug - Starting PDF generation for {'price list' if price_list_mode else 'invoice #'+str(invoice_id)}")
            customer = self.db.get_customer_by_id(customer_id)
            if not customer:
                raise ValueError(f"Customer with ID {customer_id} not found")

            # Format items properly
            formatted_items = []
            for item in items:
                formatted_items.append({
                    'name': item['name'],
                    'description': item.get('description', ''),
                    'quantity': int(float(item['quantity'])),  # Convert to integer
                    'price': float(item['price']),
                    'total': float(item['total'])
                })

            # Get discount and deposit information
            discount_value = float(self.discount_amount.get() or 0)
            discount_type = self.discount_type.get()

            # Make sure deposit_value is a float
            try:
                deposit_value = float(self.deposit_amount.get() or 0)
                print(f"Debug - Deposit value: {deposit_value}, type: {type(deposit_value)}")
            except (ValueError, TypeError) as e:
                print(f"Debug - Error converting deposit to float: {e}, value: {self.deposit_amount.get()}")
                deposit_value = 0

            # Calculate subtotal
            subtotal = sum(item['total'] for item in formatted_items)

            # Apply discount
            discount_amount = 0
            if discount_type == 'percent' and discount_value > 0:
                discount_amount = subtotal * (discount_value / 100)
            elif discount_type == 'amount' and discount_value > 0:
                discount_amount = min(discount_value, subtotal)

            # Calculate discounted subtotal
            discounted_subtotal = subtotal - discount_amount

            # Calculate VAT
            vat_rate = float(self.vat_rate.get() or 0)  # Use the current VAT rate from UI
            vat_amount = discounted_subtotal * (vat_rate / 100)
            total_amount = discounted_subtotal + vat_amount
            print(f"Debug - Total amount: {total_amount}, type: {type(total_amount)}")

            # Get deposit date from UI
            deposit_date_value = self.deposit_date.get().strip() if self.deposit_date.get().strip() else None

            # Prepare invoice data
            data = {
                'invoice_number': invoice_id,
                'date': datetime.now().strftime('%Y-%m-%d'),
                'customer_name': customer[1],
                'customer_contact': customer[2] if customer[2] else "N/A",
                'customer_address': customer[3] if customer[3] else "N/A",
                'items': formatted_items,
                'subtotal': subtotal,
                'discount_type': discount_type,
                'discount_value': discount_value,
                'discount_amount': discount_amount,
                'discounted_subtotal': discounted_subtotal,
                'vat_rate': vat_rate,
                'vat_amount': vat_amount,
                'deposit_amount': deposit_value,
                'deposit_date': deposit_date_value,
                'total': total_amount,
                'balance_due': float(total_amount) - float(deposit_value)  # Ensure both are floats
            }

            # Always use a directory in the user's Documents folder for invoices
            # This ensures we have write permissions even when running from an installed version
            invoices_dir = os.path.join(os.path.expanduser("~"), "Documents", "TLT_Invoices")

            print(f"Debug - Invoices directory path: {invoices_dir}")

            # Ensure invoices directory exists
            try:
                os.makedirs(invoices_dir, exist_ok=True)
                print(f"Debug - Invoices directory created at: {invoices_dir}")
            except Exception as e:
                print(f"Debug - Error creating invoices directory: {e}")
                # If we can't create the directory in Documents, try a temp directory
                import tempfile
                invoices_dir = tempfile.gettempdir()
                print(f"Debug - Using temp directory as fallback: {invoices_dir}")

            # Base filename
            if price_list_mode:
                # For price lists, use a different naming convention
                customer_name = customer[1].replace(' ', '_') if customer[1] else 'customer'
                base_filename = f'price_list_{customer_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
                filename = os.path.join(invoices_dir, f'{base_filename}.pdf')
            elif isinstance(invoice_id, str):
                # For custom number invoices, create a temporary file that will be auto-deleted
                import tempfile
                # Create a temporary file with .pdf extension
                temp_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
                temp_file.close()  # Close the file so we can use it
                filename = temp_file.name
                # Store the temp filename so we can register it for deletion when app closes
                if not hasattr(self, 'temp_files'):
                    self.temp_files = []
                self.temp_files.append(filename)
                # Register a cleanup function if not already done
                if not hasattr(self, 'cleanup_registered'):
                    import atexit
                    atexit.register(self.cleanup_temp_files)
                    self.cleanup_registered = True
            else:
                # For regular invoices, use invoice ID
                base_filename = f'invoice_{invoice_id}'

                # Check if this is an edited invoice
                version = 1
                while os.path.exists(os.path.join(invoices_dir, f'{base_filename}_v{version}.pdf')):
                    version += 1

                # Generate filename with version number if needed
                if version > 1:
                    filename = os.path.join(invoices_dir, f'{base_filename}_v{version}.pdf')
                else:
                    filename = os.path.join(invoices_dir, f'{base_filename}.pdf')

            print(f"Debug - PDF will be saved to: {filename}")

            # Generate invoice or price list using custom template
            # Import the invoice generator function to ensure it's available
            from ..utils.invoice_template import generate_custom_invoice

            if price_list_mode:
                # For price list mode, use special settings
                print(f"Debug - Generating price list in price list mode")
                generate_custom_invoice(data, filename, {
                    'currency': '$',
                    'vat_rate': vat_rate,
                    'status': 'Price List',  # Special status for price lists
                    'is_price_list': True,   # Flag to indicate this is a price list
                    'title': 'Price List'    # Override the title
                })
            else:
                # For regular invoice, check if this is a custom invoice (string ID)
                if isinstance(invoice_id, str):
                    # This is an invoice with a custom number
                    print(f"Debug - Generating invoice with custom number: {invoice_id}")
                    status = "Paid"  # Mark custom invoices as paid
                else:
                    # Regular invoice, get status from database
                    status = self.db.get_invoice_status(invoice_id)

                print(f"Debug - Invoice #{invoice_id} status for PDF generation: {status}")

                # Generate the PDF
                print(f"Debug - Calling generate_custom_invoice function")
                generate_custom_invoice(data, filename, {
                    'currency': '$',
                    'vat_rate': vat_rate,
                    'status': status
                })

            # Verify the file was created
            if os.path.exists(filename):
                print(f"Debug - PDF file successfully created at: {filename}")
                file_size = os.path.getsize(filename)
                print(f"Debug - PDF file size: {file_size} bytes")
            else:
                print(f"Debug - PDF file was not created at: {filename}")

            return filename

        except Exception as e:
            print(f"Debug - Error generating invoice PDF: {e}")
            import traceback
            traceback.print_exc()
            raise Exception(f"Failed to generate invoice PDF: {str(e)}")

    def get_invoice_items(self):
        """Get all items from the invoice tree"""
        items = []
        for item_id in self.products_tree.get_children():
            item = self.products_tree.item(item_id)
            values = item['values']

            print(f"Debug - Raw tree values: {values}")

            # Get product name directly from the tree values
            product_name = values[1]  # Name is at index 1
            print(f"Debug - Using product name: {product_name}")

            # Get quantity and price
            quantity = int(float(values[3]))  # Quantity is at index 3
            unit_price = float(values[4].replace('$', '').replace(',', ''))  # Price is at index 4

            # Create item dictionary using name as the identifier
            item_data = {
                'name': product_name,  # Product name as identifier
                'quantity': quantity,
                'price': unit_price,
                'description': values[2] if len(values) > 2 else ''
            }

            print(f"Debug - Processed item: {item_data}")
            items.append(item_data)

        return items

    def add_item(self):
        try:
            if not self.product_cb.get():
                raise ValueError("Please select a product")

            product_str = self.product_cb.get()
            if ' (' not in product_str:
                raise ValueError("Invalid product selection")

            # Extract product name from the dropdown
            product_name = product_str.split(' (')[0].strip()

            # Get product by name
            self.db.cursor.execute(
                "SELECT * FROM inventory WHERE name = ?",
                (product_name,)
            )
            product = self.db.cursor.fetchone()

            if not product:
                raise ValueError("Product not found")

            quantity = int(self.quantity_var.get())
            if quantity <= 0:
                raise ValueError("Quantity must be greater than 0")

            if product[3] < quantity:  # Check stock
                raise ValueError(f"Insufficient stock. Only {product[3]} available.")

            unit_price = float(product[4] or 0)  # Unit price
            total_price = quantity * unit_price

            # Get product barcode and name
            product_barcode = product[1] or ""  # product[1] is barcode/product_code
            product_name = product[2]          # product[2] is name

            # Insert with consistent column structure
            values = (
                product_barcode,    # Barcode
                product_name,      # Name
                product[8] if len(product) > 8 else "",  # Description
                quantity,          # Quantity
                f"${unit_price:.2f}",   # Unit Price
                f"${total_price:.2f}"    # Total Price
            )

            # We don't need to store product ID in tags anymore since we use name
            self.products_tree.insert('', 'end', values=values)

            self.update_total()
            self.product_cb.set('')
            self.quantity_var.set('1')

        except ValueError as e:
            messagebox.showerror("Error", str(e))
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add item: {str(e)}")

    def add_custom_item(self):
        try:
            name = self.custom_name_var.get().strip()
            if not name:
                raise ValueError("Please enter a custom item name")

            description = self.custom_desc_var.get().strip()
            quantity = int(self.custom_qty_var.get())
            if quantity <= 0:
                raise ValueError("Quantity must be greater than 0")

            price = float(self.custom_price_var.get())
            if price < 0:
                raise ValueError("Price cannot be negative")

            total_price = quantity * price

            # Insert custom item into tree
            self.products_tree.insert('', 'end', values=(
                "",             # Barcode
                name,           # Name
                description,    # Description
                quantity,       # Quantity
                f"${price:.2f}", # Price
                f"${total_price:.2f}"  # Total
            ))

            self.update_total()

            # Clear custom item fields
            self.custom_name_var.set("")
            self.custom_desc_var.set("")
            self.custom_qty_var.set("1")
            self.custom_price_var.set("0.00")

        except ValueError as e:
            messagebox.showerror("Error", str(e))
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add custom item: {str(e)}")

    def print_invoice(self):
        try:
            if not self.products_tree.get_children():
                raise ValueError("No items to print")

            if not self.customer_id.get():
                raise ValueError("Please select a customer")

            # First save the invoice to the database
            invoice_id = self.save_invoice(print_after=True)
            if not invoice_id:
                return

            # Mark invoice as Paid immediately after printing
            # This will update the status regardless of whether it was previously saved as Not Paid
            self.db.update_invoice_status(invoice_id, 'Paid')
            print(f"Debug - Invoice #{invoice_id} marked as Paid after printing")

            # Get customer details
            customer_id_or_name = self.customer_id.get()

            # Check if the customer_id is a numeric ID or a name
            try:
                # Try to convert to integer - if successful, it's an ID
                customer_id = int(customer_id_or_name)
                # Verify that this customer ID exists
                customer = self.db.get_customer_by_id(customer_id)
                if not customer:
                    raise ValueError(f"Customer with ID '{customer_id}' not found in database")
            except ValueError:
                # Not an integer, so treat as a name
                customer_name = customer_id_or_name
                # Look up customer ID from the name using our dictionary
                if customer_name not in self.customer_data:
                    raise ValueError(f"Customer '{customer_name}' not found in database")
                customer_id = self.customer_data[customer_name]

            # Get items from the tree
            items = []
            for item in self.products_tree.get_children():
                values = self.products_tree.item(item)['values']

                # Convert values to string first, then handle the formatting
                price_str = str(values[4])  # Price is in the 5th column
                total_str = str(values[5])  # Total is in the 6th column

                # Remove currency symbols and commas, then convert to float
                price = float(price_str.replace('$', '').replace(',', '') if '$' in price_str else price_str)
                total = float(total_str.replace('$', '').replace(',', '') if '$' in total_str else total_str)

                items.append({
                    'name': values[1],        # Name
                    'description': values[2],  # Description
                    'quantity': int(float(values[3])),  # Quantity as integer
                    'price': price,           # Unit price
                    'total': total            # Total price
                })

            # Generate and open PDF
            filename = self.generate_invoice_pdf(invoice_id, customer_id, items)
            os.startfile(filename)

            # Clear form and show success message after printing
            self.clear_form()
            # Refresh products to reflect updated inventory
            self.refresh_products()
            messagebox.showinfo("Success", f"Invoice #{invoice_id} has been printed and marked as paid!")

            # Notify any open inventory module to refresh
            self.parent.event_generate('<<InventoryChanged>>', when='tail')

        except Exception as e:
            messagebox.showerror("Error", str(e))
            print(f"Error generating invoice: {e}")

    def print_as_price_list(self):
        """Print the current items as a price list without invoice number and totals"""
        try:
            if not self.products_tree.get_children():
                raise ValueError("No items to include in price list")

            if not self.customer_id.get():
                raise ValueError("Please select a customer")

            # Get customer details
            customer_id_or_name = self.customer_id.get()

            # Check if the customer_id is a numeric ID or a name
            try:
                # Try to convert to integer - if successful, it's an ID
                customer_id = int(customer_id_or_name)
                # Verify that this customer ID exists
                customer = self.db.get_customer_by_id(customer_id)
                if not customer:
                    raise ValueError(f"Customer with ID {customer_id} not found")
            except ValueError:
                # Not an integer, so treat as a name
                customer_name = customer_id_or_name
                # Look up customer ID from the name using our dictionary
                if customer_name not in self.customer_data:
                    raise ValueError(f"Customer '{customer_name}' not found in database")
                customer_id = self.customer_data[customer_name]

            # Get items from the treeview
            items = []
            for item_id in self.products_tree.get_children():
                values = self.products_tree.item(item_id)['values']

                # Extract price without $ sign
                price_str = str(values[4])  # Price is at index 4
                price = float(price_str.replace('$', '').replace(',', '') if '$' in price_str else price_str)

                # Calculate total
                quantity = int(float(values[3]))  # Quantity is at index 3
                total = quantity * price

                items.append({
                    'name': values[1],        # Name
                    'description': values[2],  # Description
                    'quantity': quantity,      # Quantity as integer
                    'price': price,           # Unit price
                    'total': total            # Total price
                })

            # Show confirmation dialog asking "Do you need a copy?"
            confirmation = messagebox.askyesno("Confirmation", "Do you need a copy?")

            if confirmation:
                # User clicked Yes - continue with original process
                # Generate and open PDF in price list mode
                # We use 0 as a special invoice_id that indicates this is a price list
                filename = self.generate_invoice_pdf(0, customer_id, items, price_list_mode=True)
                os.startfile(filename)

                # Don't clear the form after printing a price list
                # This allows the user to continue working with the same items
                messagebox.showinfo("Success", "Price list has been generated and opened!")
            else:
                # User clicked No - ask for a custom invoice number
                self.generate_custom_invoice(customer_id, items)

        except Exception as e:
            print(f"Error printing price list: {e}")
            messagebox.showerror("Error", f"Error generating price list: {str(e)}")

    def generate_custom_invoice(self, customer_id, items):
        """Generate an invoice with a custom invoice number"""
        try:
            # First, ask for verification code (but with the same dialog title as invoice number)
            # Use a custom approach to ensure the dialog appears in the foreground
            import tkinter as tk
            from tkinter import simpledialog

            # Create a custom dialog that will stay on top
            dialog_root = tk.Toplevel(self.parent)
            dialog_root.withdraw()  # Hide the empty window
            dialog_root.attributes('-topmost', True)  # Make it stay on top

            # Show the dialog and make sure it's in the foreground
            verification_code = simpledialog.askstring("Invoice Number", "(Copie Number)",
                                                     initialvalue="", parent=dialog_root)

            # Clean up the temporary root window
            dialog_root.destroy()

            if verification_code is None:  # User cancelled
                return

            # Check if the verification code is correct (101)
            if verification_code.strip() != "101":
                print("Verification code incorrect - closing process")
                return

            # If verification code is correct, now ask for the actual invoice number
            # Use a custom approach to ensure the dialog appears in the foreground
            import tkinter as tk
            from tkinter import simpledialog

            # Create a custom dialog that will stay on top
            dialog_root = tk.Toplevel(self.parent)
            dialog_root.withdraw()  # Hide the empty window
            dialog_root.attributes('-topmost', True)  # Make it stay on top

            # Show the dialog and make sure it's in the foreground
            invoice_number = simpledialog.askstring("Invoice Number", "Enter invoice number:",
                                                  initialvalue="", parent=dialog_root)

            # Clean up the temporary root window
            dialog_root.destroy()

            if invoice_number is None:  # User cancelled
                return

            if not invoice_number.strip():
                messagebox.showerror("Error", "Invoice number cannot be empty")
                return

            # Generate the invoice PDF with the custom number
            # We use the entered invoice number but don't save it to the database
            filename = self.generate_invoice_pdf(invoice_number, customer_id, items, price_list_mode=False)

            # Open the generated PDF
            os.startfile(filename)

            # Show success message
            messagebox.showinfo("Success", f"Invoice #{invoice_number} has been generated.")

        except Exception as e:
            print(f"Error generating invoice: {e}")
            messagebox.showerror("Error", f"Error generating invoice: {str(e)}")

    def cleanup_temp_files(self):
        """Clean up temporary invoice files when the application exits"""
        if hasattr(self, 'temp_files'):
            print(f"Cleaning up {len(self.temp_files)} temporary invoice files")
            for temp_file in self.temp_files:
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        print(f"Deleted temporary file: {temp_file}")
                except Exception as e:
                    print(f"Error deleting temporary file {temp_file}: {e}")

    def update_total(self):
        """Calculate and update total amount with discount and VAT"""
        try:
            # Calculate subtotal from all items
            subtotal = 0
            for item in self.products_tree.get_children():
                values = self.products_tree.item(item)['values']
                # Total price is at index 5
                total_price_str = str(values[5])
                # Remove currency symbols and commas, then convert to float
                item_total = float(total_price_str.replace('$', '').replace(',', '') if '$' in total_price_str else total_price_str)
                subtotal += item_total

            # Apply discount
            discount_amount = 0
            try:
                discount_value = float(self.discount_amount.get() or 0)
                discount_type = self.discount_type.get()

                if discount_type == 'percent' and discount_value > 0:
                    # Percentage discount
                    discount_amount = subtotal * (discount_value / 100)
                    print(f"Debug - Percent discount: {subtotal} * {discount_value}% = {discount_amount}")
                elif discount_type == 'amount' and discount_value > 0:
                    # Fixed amount discount
                    discount_amount = min(discount_value, subtotal)  # Can't discount more than the subtotal
                    print(f"Debug - Amount discount: ${discount_amount}")
            except ValueError:
                # Invalid discount value
                discount_amount = 0
                print("Debug - Invalid discount value")

            # Calculate discounted subtotal
            discounted_subtotal = subtotal - discount_amount

            # Apply VAT to the discounted subtotal
            vat_rate = float(self.vat_rate.get() or 0)
            vat_amount = discounted_subtotal * (vat_rate / 100)
            total = discounted_subtotal + vat_amount

            # Update display
            self.subtotal_var.set(f"{subtotal:.2f}")
            self.total_var.set(f"{total:.2f}")

            print(f"Debug - Subtotal: ${subtotal:.2f}, Discount: ${discount_amount:.2f}, VAT: ${vat_amount:.2f}, Total: ${total:.2f}")

        except Exception as e:
            print(f"Debug - Total calculation error: {e}")  # Debug print
            self.subtotal_var.set("0.00")
            self.total_var.set("0.00")

    def clear_form(self):
        """Clear all form fields"""
        if self.customer_cb:
            self.customer_cb.set('')
        if self.product_cb:
            self.product_cb.set('')
        if self.barcode_entry:
            self.barcode_var.set('')
        self.quantity_var.set('1')
        if self.products_tree:
            self.products_tree.delete(*self.products_tree.get_children())
        self.total_var.set('0.00')
        self.subtotal_var.set('0.00')
        self.discount_amount.set('0')
        self.discount_type.set('amount')
        self.deposit_amount.set('0')
        self.deposit_date.set('')

        # Reset the current invoice ID
        self.current_invoice_id = None

        # Reset the edit mode label
        if hasattr(self, 'edit_mode_label'):
            self.edit_mode_label.config(text="")

        # Reset the save button text
        if hasattr(self, 'save_button'):
            self.save_button.config(text="Save Invoice")

    def show_item_menu(self, event):
        """Show context menu for item management"""
        # Check if any item is selected
        selected = self.products_tree.identify_row(event.y)
        if not selected:
            return

        # Select the item under cursor
        self.products_tree.selection_set(selected)
        # Show the menu
        self.item_menu.post(event.x_root, event.y_root)

    def increase_quantity(self):
        """Increase quantity of selected item by 1"""
        selected = self.products_tree.selection()
        if not selected:
            return

        # Get current values
        item = self.products_tree.item(selected[0])
        values = item['values']

        # Get product name
        product_name = values[1]  # Name is at index 1

        # Get current quantity and increase by 1
        current_qty = int(values[3])  # Quantity is at index 3
        new_qty = current_qty + 1

        # Check if we have enough stock (only for products in inventory)
        self.db.cursor.execute(
            "SELECT quantity FROM inventory WHERE name = ?",
            (product_name,)
        )
        result = self.db.cursor.fetchone()

        # If product exists in inventory, check stock
        if result:
            available = result[0]
            if available < new_qty:
                messagebox.showerror("Error", f"Insufficient stock. Only {available} available.")
                return

        # Calculate new total
        unit_price = float(values[4].replace('$', ''))  # Price is at index 4
        new_total = new_qty * unit_price

        # Update the item
        self.products_tree.item(selected[0], values=(
            values[0],  # barcode
            values[1],  # name
            values[2],  # description
            new_qty,    # quantity
            values[4],  # price
            f"${new_total:.2f}"  # total
        ))

        # Update invoice total
        self.update_total()

    def decrease_quantity(self):
        """Decrease quantity of selected item by 1"""
        selected = self.products_tree.selection()
        if not selected:
            return

        # Get current values
        item = self.products_tree.item(selected[0])
        values = item['values']

        # Get current quantity and decrease by 1
        current_qty = int(values[3])  # Quantity is at index 3
        if current_qty <= 1:
            # If quantity would be 0 or less, remove the item
            self.remove_item()
            return

        new_qty = current_qty - 1

        # Calculate new total
        unit_price = float(values[4].replace('$', ''))  # Price is at index 4
        new_total = new_qty * unit_price

        # Update the item
        self.products_tree.item(selected[0], values=(
            values[0],  # barcode
            values[1],  # name
            values[2],  # description
            new_qty,    # quantity
            values[4],  # price
            f"${new_total:.2f}"  # total
        ))

        # Update invoice total
        self.update_total()

    def remove_item(self):
        """Remove selected item from invoice"""
        selected = self.products_tree.selection()
        if not selected:
            return

        # Remove the item
        self.products_tree.delete(selected[0])

        # Update invoice total
        self.update_total()

    def edit_quantity(self, event):
        """Edit quantity of selected item via double-click"""
        selected = self.products_tree.selection()
        if not selected:
            # If no item is selected, try to get the item that was clicked
            item = self.products_tree.identify('item', event.x, event.y)
            if not item:
                return
            selected = [item]
            self.products_tree.selection_set(item)

        # Get current values
        item = self.products_tree.item(selected[0])
        values = item['values']

        # Get product name and current quantity
        product_name = values[1]  # Name is at index 1
        current_qty = int(values[3])  # Quantity is at index 3

        # Ask for new quantity
        new_qty = simpledialog.askinteger("Edit Quantity",
                                        f"Enter new quantity for {product_name}:",
                                        initialvalue=current_qty,
                                        minvalue=1)

        if new_qty is None:  # User cancelled
            return

        # Check if we have enough stock (only for products in inventory)
        self.db.cursor.execute(
            "SELECT quantity FROM inventory WHERE name = ?",
            (product_name,)
        )
        result = self.db.cursor.fetchone()

        # If product exists in inventory, check stock
        if result:
            available = result[0]
            if available < new_qty:
                messagebox.showerror("Error", f"Insufficient stock. Only {available} available.")
                return

        # Calculate new total
        unit_price = float(values[4].replace('$', ''))  # Price is at index 4
        new_total = new_qty * unit_price

        # Update the item
        self.products_tree.item(selected[0], values=(
            values[0],  # barcode
            values[1],  # name
            values[2],  # description
            new_qty,    # quantity
            values[4],  # price
            f"${new_total:.2f}"  # total
        ))

        # Update invoice total
        self.update_total()

    def load_invoice_for_editing(self, invoice_id):
        """Load an existing invoice for editing"""
        try:
            # Clear the current invoice first
            self.clear_invoice()

            # Get the invoice details from the database
            invoice = self.db.get_invoice_by_id(invoice_id)
            if not invoice:
                messagebox.showerror("Error", f"Invoice #{invoice_id} not found")
                return

            # Set the current invoice ID for updating instead of creating a new one
            self.current_invoice_id = invoice_id

            # Get customer ID from the invoice
            customer_id = invoice[1]  # Assuming invoice[1] is customer_id

            # Get customer details
            customer = self.db.get_customer_by_id(customer_id)
            customer_name = customer[1] if customer and len(customer) > 1 else "Unknown"

            # Update the edit mode label to show customer name
            if hasattr(self, 'edit_mode_label'):
                self.edit_mode_label.config(text=f"Editing Invoice #{invoice_id} for {customer_name}")

            # Update the save button text
            if hasattr(self, 'save_button'):
                self.save_button.config(text="Update Invoice")

            # Get customer ID and set the customer dropdown
            customer_id = invoice[1]  # Assuming invoice[1] is customer_id

            # Make sure we have the latest customer data
            self.refresh_customers(silent=True)

            # Get customer details
            customer = self.db.get_customer_by_id(customer_id)
            if customer and self.customer_cb:
                customer_name = customer[1]  # Assuming customer[1] is name

                # Update the customer data dictionary if needed
                if customer_name not in self.customer_data:
                    self.customer_data[customer_name] = customer_id
                    # Update the dropdown values
                    self.customer_cb['values'] = list(self.customer_data.keys())

                # Set the customer in the dropdown
                self.customer_cb.set(customer_name)

                # Store the customer ID for later use
                self.customer_id.set(str(customer_id))

            # Set VAT rate
            vat_rate = invoice[4] if len(invoice) > 4 else 0
            self.vat_rate.set(str(vat_rate))

            # Set discount
            discount_amount = invoice[7] if len(invoice) > 7 else 0
            discount_type = invoice[8] if len(invoice) > 8 else 'amount'
            self.discount_amount.set(str(discount_amount))
            self.discount_type.set(discount_type)

            # Set deposit amount
            deposit_amount = invoice[9] if len(invoice) > 9 else 0
            # Make sure deposit_amount is a float
            if deposit_amount is not None:
                try:
                    deposit_amount = float(deposit_amount)
                except (ValueError, TypeError):
                    deposit_amount = 0
            else:
                deposit_amount = 0
            self.deposit_amount.set(str(deposit_amount))

            # Set deposit date
            deposit_date = invoice[10] if len(invoice) > 10 else None
            if deposit_date:
                self.deposit_date.set(str(deposit_date))
            else:
                self.deposit_date.set("")

            # Get invoice items
            items = self.db.get_invoice_items(invoice_id)

            # Debug - print all items
            print(f"Debug - Loading {len(items)} items for invoice #{invoice_id}")
            for i, item in enumerate(items):
                print(f"Item {i+1}: {item}")

            # Add items to the invoice
            for item in items:
                try:
                    # The get_invoice_items query returns the following columns:
                    # 0: i.id (inventory id)
                    # 1: i.product_code (barcode)
                    # 2: i.name (product name from inventory)
                    # 3: ii.quantity (quantity from invoice_items)
                    # 4: i.unit_price (price from inventory)
                    # 5: i.cost_price
                    # 6: i.category
                    # 7: i.min_stock
                    # 8: i.description (description from inventory)
                    # 9: ii.item_name (custom item name from invoice_items)
                    # 10: ii.description (custom description from invoice_items)
                    # 11: ii.price (price from invoice_items)

                    # Get quantity from invoice_items
                    quantity = item[3]

                    # Get price from invoice_items (more accurate than inventory price)
                    price = item[11] if len(item) > 11 and item[11] is not None else item[4]

                    # Calculate total price
                    total_price = quantity * price

                    # Get product details - prefer invoice_items data over inventory data
                    # For barcode, use inventory.product_code
                    product_barcode = item[1] if item[1] is not None else ""

                    # For name, use invoice_items.item_name if available, otherwise use inventory.name
                    product_name = ""
                    if len(item) > 9 and item[9] is not None and item[9].strip():
                        product_name = item[9]  # Use item_name from invoice_items
                    elif item[2] is not None and item[2].strip():
                        product_name = item[2]  # Use name from inventory
                    else:
                        product_name = "Custom Item"  # Fallback

                    # For description, use invoice_items.description if available, otherwise use inventory.description
                    description = ""
                    if len(item) > 10 and item[10] is not None and item[10].strip():
                        description = item[10]  # Use description from invoice_items
                    elif len(item) > 8 and item[8] is not None and item[8].strip():
                        description = item[8]  # Use description from inventory

                    # Debug - print item details
                    print(f"Processing item: Barcode={product_barcode}, Name={product_name}, Desc={description}, Qty={quantity}, Price=${price:.2f}")

                    # Add to invoice
                    self.products_tree.insert('', 'end', values=(
                        product_barcode,  # Barcode
                        product_name,     # Name
                        description,      # Description
                        quantity,         # Quantity
                        f"${price:.2f}",  # Unit Price
                        f"${total_price:.2f}"  # Total Price
                    ))

                    print(f"Added item to invoice: {product_name}, {description}, {quantity}, ${price:.2f}")

                except Exception as e:
                    print(f"Error loading invoice item: {e}")

            # Update totals
            self.update_total()

            # Show a message to indicate we're editing an existing invoice
            messagebox.showinfo("Edit Invoice", f"Loaded invoice #{invoice_id} for editing\n\nYou can now modify this invoice by:\n- Adding new items\n- Changing quantities (double-click on quantity)\n- Removing items (right-click and select Remove)\n\nClick Save Invoice when done.")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load invoice: {str(e)}")
            print(f"Error loading invoice: {e}")

    def clear_invoice(self):
        """Clear the current invoice and reset all fields"""
        try:
            # Clear the products tree
            if self.products_tree:
                for item in self.products_tree.get_children():
                    self.products_tree.delete(item)

            # Reset variables
            self.current_invoice_id = None
            self.quantity_var.set("1")
            self.total_var.set("0.00")
            self.subtotal_var.set("0.00")
            self.discount_amount.set("0")
            self.discount_type.set("amount")

            # Clear barcode field
            if self.barcode_entry:
                self.barcode_entry.delete(0, tk.END)

            # Update totals
            self.update_total()

        except Exception as e:
            print(f"Error clearing invoice: {e}")

    def scan_barcode(self, _=None):
        """Process barcode scan and add product to invoice"""
        try:
            barcode = self.barcode_var.get().strip()
            if not barcode:
                return

            # Search for product by barcode
            self.db.cursor.execute(
                "SELECT * FROM inventory WHERE product_code = ?",
                (barcode,)
            )
            product = self.db.cursor.fetchone()

            if not product:
                messagebox.showerror("Error", f"No product found with barcode: {barcode}")
                self.barcode_var.set("")
                return

            # Check if product has stock
            if product[3] <= 0:  # product[3] is quantity
                messagebox.showerror("Error", f"No stock available for {product[2]}")
                self.barcode_var.set("")
                return

            # Add product to invoice with quantity 1
            quantity = 1
            unit_price = float(product[4] or 0)  # Unit price
            total_price = quantity * unit_price
            product_barcode = product[1] or ""  # product[1] is barcode/product_code
            product_name = product[2]          # product[2] is name

            # Insert with consistent column structure
            self.products_tree.insert('', 'end', values=(
                product_barcode,  # Barcode
                product_name,     # Name
                product[8] if len(product) > 8 else "",  # Description
                quantity,         # Quantity
                f"${unit_price:.2f}",  # Unit Price
                f"${total_price:.2f}"  # Total Price
            ))

            self.update_total()
            self.barcode_var.set("")  # Clear barcode field

            # Set focus back to barcode entry for next scan
            if self.barcode_entry:
                self.barcode_entry.focus()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to process barcode: {str(e)}")
            self.barcode_var.set("")
