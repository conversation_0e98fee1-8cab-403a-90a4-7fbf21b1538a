2025-06-09 12:24:42,322 - root - INFO - ===== TLT Group Management System Starting =====
2025-06-09 12:24:42,323 - root - INFO - Log file created at: C:\Users\<USER>\Desktop\music project\Accounting101\logs\application_20250609_122442.log
2025-06-09 12:24:42,698 - root - INFO - Searching for icon in: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-09 12:24:42,763 - root - INFO - Set icon using ICO file: C:\Users\<USER>\Desktop\music project\Accounting101\resources\TLT icon.ico
2025-06-09 12:24:42,903 - root - INFO - Found existing database at: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-09 12:24:42,905 - root - INFO - Selected database path: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-09 12:24:42,906 - root - INFO - Ensured database directory exists: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-09 12:24:42,912 - root - INFO - Database initialized successfully with WAL journal mode
2025-06-09 12:24:42,916 - root - INFO - Database check completed successfully
2025-06-09 12:24:42,918 - root - INFO - Loading module: Sales
2025-06-09 12:24:43,064 - root - INFO - Successfully loaded module: Sales
2025-06-09 12:24:43,065 - root - INFO - Loading module: Customers
2025-06-09 12:24:43,194 - root - INFO - Successfully loaded module: Customers
2025-06-09 12:24:43,195 - root - INFO - Loading module: Suppliers
2025-06-09 12:24:43,216 - modules.utils.table_customizer - INFO - Applied column order for suppliers: ['name', 'contact', 'address', 'balance']
2025-06-09 12:24:43,216 - modules.utils.table_customizer - INFO - Loaded saved column order for suppliers
2025-06-09 12:24:43,230 - root - INFO - Successfully loaded module: Suppliers
2025-06-09 12:24:43,230 - root - INFO - Loading module: Purchases
2025-06-09 12:24:43,287 - root - INFO - Successfully loaded module: Purchases
2025-06-09 12:24:43,287 - root - INFO - Loading module: Inventory
2025-06-09 12:24:43,379 - modules.utils.barcode_generator_new - INFO - Successfully imported PIL modules
2025-06-09 12:24:43,448 - modules.utils.barcode_generator_new - INFO - Successfully imported and tested barcode module
2025-06-09 12:24:43,605 - modules.utils.label_generator - INFO - Successfully imported reportlab modules
2025-06-09 12:24:43,664 - modules.utils.table_customizer - INFO - Applied column order for inventory: ['barcode', 'name', 'quantity', 'price', 'cost price', 'category', 'min stock', 'description']
2025-06-09 12:24:43,664 - modules.utils.table_customizer - INFO - Loaded saved column order for inventory
2025-06-09 12:24:43,669 - root - INFO - Successfully loaded module: Inventory
2025-06-09 12:24:43,670 - root - INFO - Loading module: Reports
2025-06-09 12:24:43,742 - root - INFO - Successfully loaded module: Reports
2025-06-09 12:24:43,742 - root - INFO - Loading module: Settings
2025-06-09 12:24:43,839 - root - INFO - Successfully loaded module: Settings
2025-06-09 12:24:43,839 - root - INFO - Application starting in unlocked state
2025-06-09 12:24:43,839 - root - INFO - Initializing security timer
2025-06-09 12:24:43,840 - root - INFO - Loaded security delay from config: 0 seconds
2025-06-09 12:24:43,840 - root - INFO - SecurityTimer initialized with delay: 0 seconds
2025-06-09 12:24:43,840 - root - INFO - Security timer initialized successfully
2025-06-09 12:24:43,840 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-09 12:24:46,287 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-09 12:24:46,287 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
