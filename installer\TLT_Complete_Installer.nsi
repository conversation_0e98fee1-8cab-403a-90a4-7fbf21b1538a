;===============================================================================
; TLT Group Management System - Complete NSIS Installer
; Created for TLT Group SARL
; Version: 5.0.0 - Complete Business Management Solution with All Modules
;===============================================================================

;--------------------------------
; Include Modern UI
!include "MUI2.nsh"
!include "FileFunc.nsh"
!include "LogicLib.nsh"

;--------------------------------
; General Settings
!define APPNAME "TLT Group Management System"
!define COMPANYNAME "TLT Group SARL"
!define DESCRIPTION "Complete Business Management Solution"
!define VERSION "5.0.0"
!define HELPURL "https://tltgroup.com/support"
!define UPDATEURL "https://tltgroup.com/updates"
!define ABOUTURL "https://tltgroup.com"

Name "${APPNAME}"
OutFile "TLT_Group_Management_System_Complete_v${VERSION}.exe"
Unicode True
BrandingText "${COMPANYNAME} - ${DESCRIPTION}"

; Installation directory
InstallDir "$PROGRAMFILES64\TLT Group\Management System"
InstallDirRegKey HKLM "Software\${COMPANYNAME}\${APPNAME}" "InstallLocation"

; Request admin privileges
RequestExecutionLevel admin

; Compression
SetCompressor /SOLID lzma
SetCompressorDictSize 32

;--------------------------------
; Variables
Var StartMenuFolder

;--------------------------------
; Interface Settings
!define MUI_ABORTWARNING
!define MUI_ICON "..\resources\TLT icon.ico"
!define MUI_UNICON "..\resources\TLT icon.ico"

; Welcome page
!define MUI_WELCOMEPAGE_TITLE "Welcome to ${APPNAME} Setup"
!define MUI_WELCOMEPAGE_TEXT "This wizard will guide you through the installation of ${APPNAME}.$\r$\n$\r$\nThis complete business management solution includes:$\r$\n• Customer Management$\r$\n• Inventory Control$\r$\n• Invoice Generation with Deposit Tracking$\r$\n• Financial Reports$\r$\n• Supplier Management$\r$\n• Purchase Management$\r$\n• Sales Management$\r$\n$\r$\nClick Next to continue."

; Finish page
!define MUI_FINISHPAGE_RUN "$INSTDIR\run_app.bat"
!define MUI_FINISHPAGE_RUN_TEXT "Start ${APPNAME}"

;--------------------------------
; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY

; Start Menu Folder Page
!define MUI_STARTMENUPAGE_DEFAULTFOLDER "${APPNAME}"
!define MUI_STARTMENUPAGE_REGISTRY_ROOT "HKLM"
!define MUI_STARTMENUPAGE_REGISTRY_KEY "Software\${COMPANYNAME}\${APPNAME}"
!define MUI_STARTMENUPAGE_REGISTRY_VALUENAME "Start Menu Folder"
!insertmacro MUI_PAGE_STARTMENU Application $StartMenuFolder

!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; Uninstaller pages
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

;--------------------------------
; Languages
!insertmacro MUI_LANGUAGE "English"
!insertmacro MUI_LANGUAGE "French"

;--------------------------------
; Version Information
VIProductVersion "${VERSION}.0"
VIAddVersionKey /LANG=${LANG_ENGLISH} "ProductName" "${APPNAME}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "CompanyName" "${COMPANYNAME}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "LegalCopyright" "© 2025 ${COMPANYNAME}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "FileDescription" "${DESCRIPTION}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "FileVersion" "${VERSION}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "ProductVersion" "${VERSION}"

;--------------------------------
; Installer Sections

Section "!${APPNAME} (Required)" SecMain
  SectionIn RO
  
  DetailPrint "Installing ${APPNAME} core application..."
  
  ; Set output path
  SetOutPath $INSTDIR
  
  ; Copy main application files
  File "..\main.py"
  File "..\requirements.txt"
  File /nonfatal "..\README.md"
  
  ; Copy all modules
  DetailPrint "Installing application modules..."
  SetOutPath "$INSTDIR\modules"
  File /r "..\modules\*.*"
  
  ; Copy resources
  DetailPrint "Installing resources..."
  SetOutPath "$INSTDIR\resources"
  File /r "..\resources\*.*"
  
  ; Copy config
  DetailPrint "Installing configuration..."
  SetOutPath "$INSTDIR\config"
  File /r "..\config\*.*"
  
  ; Create application directories
  DetailPrint "Creating application directories..."
  CreateDirectory "$INSTDIR\logs"
  CreateDirectory "$INSTDIR\backups"
  CreateDirectory "$INSTDIR\reports"
  CreateDirectory "$INSTDIR\invoices"
  CreateDirectory "$INSTDIR\labels"
  CreateDirectory "$INSTDIR\barcodes"
  CreateDirectory "$INSTDIR\data"
  
  ; Create user data directories
  CreateDirectory "$DOCUMENTS\TLT_Invoices"
  CreateDirectory "$DOCUMENTS\TLT_Reports"
  CreateDirectory "$DOCUMENTS\TLT_Backups"
  
  ; Create run script
  SetOutPath $INSTDIR
  FileOpen $0 "$INSTDIR\run_app.bat" w
  FileWrite $0 "@echo off$\r$\n"
  FileWrite $0 "cd /d $\"$INSTDIR$\"$\r$\n"
  FileWrite $0 "python main.py$\r$\n"
  FileWrite $0 "pause$\r$\n"
  FileClose $0
  
  ; Store installation info
  WriteRegStr HKLM "Software\${COMPANYNAME}\${APPNAME}" "InstallLocation" $INSTDIR
  WriteRegStr HKLM "Software\${COMPANYNAME}\${APPNAME}" "Version" "${VERSION}"
  
  ; Create uninstaller
  WriteUninstaller "$INSTDIR\Uninstall.exe"
  
  ; Add to Add/Remove Programs
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayName" "${APPNAME}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "UninstallString" "$\"$INSTDIR\Uninstall.exe$\""
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "InstallLocation" "$INSTDIR"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayIcon" "$INSTDIR\resources\TLT icon.ico"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "Publisher" "${COMPANYNAME}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayVersion" "${VERSION}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "URLInfoAbout" "${ABOUTURL}"
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "NoModify" 1
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "NoRepair" 1
  
  ; Calculate installation size
  ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
  IntFmt $0 "0x%08X" $0
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "EstimatedSize" "$0"

SectionEnd

Section "Python Dependencies" SecPython
  DetailPrint "Installing Python dependencies..."
  
  ; Check if Python is installed
  nsExec::ExecToStack 'python --version'
  Pop $0
  
  ${If} $0 == 0
    DetailPrint "Python found, installing dependencies..."
    nsExec::ExecToLog 'python -m pip install --upgrade pip'
    nsExec::ExecToLog 'python -m pip install -r "$INSTDIR\requirements.txt"'
    Pop $0
    
    ${If} $0 == 0
      DetailPrint "Dependencies installed successfully"
      WriteRegStr HKLM "Software\${COMPANYNAME}\${APPNAME}" "PythonDependencies" "Installed"
    ${Else}
      DetailPrint "Warning: Some dependencies may not have installed correctly"
      MessageBox MB_ICONEXCLAMATION "Some Python dependencies failed to install.$\n$\nYou may need to install them manually using:$\n$\npip install -r requirements.txt"
    ${EndIf}
  ${Else}
    DetailPrint "Python not found"
    MessageBox MB_ICONEXCLAMATION|MB_YESNO "Python not found!$\n$\nThe ${APPNAME} requires Python 3.8 or higher.$\n$\nWould you like to download Python now?" IDYES download_python IDNO skip_python
    
    download_python:
      ExecShell "open" "https://www.python.org/downloads/"
      MessageBox MB_ICONINFORMATION "Please install Python and then run the application.$\n$\nMake sure to check 'Add Python to PATH' during installation."
      Goto end_python_check
    
    skip_python:
      MessageBox MB_ICONINFORMATION "You can install Python later from https://python.org$\n$\nThe application will not work until Python is installed."
    
    end_python_check:
  ${EndIf}
SectionEnd

Section "Desktop Shortcut" SecDesktop
  CreateShortcut "$DESKTOP\${APPNAME}.lnk" "$INSTDIR\run_app.bat" "" "$INSTDIR\resources\TLT icon.ico" 0
SectionEnd

Section "Start Menu Shortcuts" SecStartMenu
  !insertmacro MUI_STARTMENU_WRITE_BEGIN Application
    CreateDirectory "$SMPROGRAMS\$StartMenuFolder"
    CreateShortcut "$SMPROGRAMS\$StartMenuFolder\${APPNAME}.lnk" "$INSTDIR\run_app.bat" "" "$INSTDIR\resources\TLT icon.ico"
    CreateShortcut "$SMPROGRAMS\$StartMenuFolder\Uninstall.lnk" "$INSTDIR\Uninstall.exe"
  !insertmacro MUI_STARTMENU_WRITE_END
SectionEnd

Section "File Associations" SecFileAssoc
  DetailPrint "Setting up file associations..."
  
  ; Associate .tlt files with the application
  WriteRegStr HKCR ".tlt" "" "TLTGroupFile"
  WriteRegStr HKCR "TLTGroupFile" "" "TLT Group Data File"
  WriteRegStr HKCR "TLTGroupFile\DefaultIcon" "" "$INSTDIR\resources\TLT icon.ico"
  WriteRegStr HKCR "TLTGroupFile\shell\open\command" "" '"$INSTDIR\run_app.bat" "%1"'
  
  ; Associate .tltbackup files
  WriteRegStr HKCR ".tltbackup" "" "TLTGroupBackup"
  WriteRegStr HKCR "TLTGroupBackup" "" "TLT Group Backup File"
  WriteRegStr HKCR "TLTGroupBackup\DefaultIcon" "" "$INSTDIR\resources\TLT icon.ico"
  WriteRegStr HKCR "TLTGroupBackup\shell\open\command" "" '"$INSTDIR\run_app.bat" "%1"'
  
  ; Refresh shell
  System::Call 'shell32.dll::SHChangeNotify(l, l, i, i) v (0x08000000, 0, 0, 0)'
SectionEnd

;--------------------------------
; Descriptions
LangString DESC_SecMain ${LANG_ENGLISH} "Core application files and all modules (required). Complete business management system with customer, inventory, invoice, and supplier management."
LangString DESC_SecPython ${LANG_ENGLISH} "Automatically installs required Python packages. Requires internet connection and Python 3.8+."
LangString DESC_SecDesktop ${LANG_ENGLISH} "Creates a desktop shortcut for quick access."
LangString DESC_SecStartMenu ${LANG_ENGLISH} "Creates Start Menu shortcuts."
LangString DESC_SecFileAssoc ${LANG_ENGLISH} "Associates TLT file types with the application."

LangString DESC_SecMain ${LANG_FRENCH} "Fichiers principaux et tous les modules (requis). Système de gestion d'entreprise complet avec gestion des clients, inventaire, factures et fournisseurs."
LangString DESC_SecPython ${LANG_FRENCH} "Installe automatiquement les packages Python requis. Nécessite une connexion Internet et Python 3.8+."
LangString DESC_SecDesktop ${LANG_FRENCH} "Crée un raccourci sur le bureau pour un accès rapide."
LangString DESC_SecStartMenu ${LANG_FRENCH} "Crée des raccourcis dans le menu Démarrer."
LangString DESC_SecFileAssoc ${LANG_FRENCH} "Associe les types de fichiers TLT avec l'application."

!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SecMain} $(DESC_SecMain)
  !insertmacro MUI_DESCRIPTION_TEXT ${SecPython} $(DESC_SecPython)
  !insertmacro MUI_DESCRIPTION_TEXT ${SecDesktop} $(DESC_SecDesktop)
  !insertmacro MUI_DESCRIPTION_TEXT ${SecStartMenu} $(DESC_SecStartMenu)
  !insertmacro MUI_DESCRIPTION_TEXT ${SecFileAssoc} $(DESC_SecFileAssoc)
!insertmacro MUI_FUNCTION_DESCRIPTION_END

;--------------------------------
; Uninstaller Section
Section "Uninstall"
  ; Stop running instances
  nsExec::Exec 'taskkill /f /im python.exe /fi "WINDOWTITLE eq ${APPNAME}*"'
  nsExec::Exec 'taskkill /f /im pythonw.exe /fi "WINDOWTITLE eq ${APPNAME}*"'

  ; Remove files and directories
  Delete "$INSTDIR\main.py"
  Delete "$INSTDIR\requirements.txt"
  Delete "$INSTDIR\README.md"
  Delete "$INSTDIR\run_app.bat"
  Delete "$INSTDIR\Uninstall.exe"

  RMDir /r "$INSTDIR\modules"
  RMDir /r "$INSTDIR\resources"
  RMDir /r "$INSTDIR\config"
  RMDir /r "$INSTDIR\logs"
  RMDir /r "$INSTDIR\backups"
  RMDir /r "$INSTDIR\reports"
  RMDir /r "$INSTDIR\invoices"
  RMDir /r "$INSTDIR\labels"
  RMDir /r "$INSTDIR\barcodes"
  RMDir /r "$INSTDIR\data"

  RMDir "$INSTDIR"
  RMDir "$PROGRAMFILES64\TLT Group"

  ; Remove shortcuts
  Delete "$DESKTOP\${APPNAME}.lnk"
  !insertmacro MUI_STARTMENU_GETFOLDER Application $StartMenuFolder
  Delete "$SMPROGRAMS\$StartMenuFolder\${APPNAME}.lnk"
  Delete "$SMPROGRAMS\$StartMenuFolder\Uninstall.lnk"
  RMDir "$SMPROGRAMS\$StartMenuFolder"

  ; Remove file associations
  DeleteRegKey HKCR ".tlt"
  DeleteRegKey HKCR "TLTGroupFile"
  DeleteRegKey HKCR ".tltbackup"
  DeleteRegKey HKCR "TLTGroupBackup"
  System::Call 'shell32.dll::SHChangeNotify(l, l, i, i) v (0x08000000, 0, 0, 0)'

  ; Remove registry keys
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}"
  DeleteRegKey HKLM "Software\${COMPANYNAME}\${APPNAME}"

  ; Ask about user data
  MessageBox MB_YESNO "Do you want to keep your data files in Documents?" IDYES preserve_data
  RMDir /r "$DOCUMENTS\TLT_Invoices"
  RMDir /r "$DOCUMENTS\TLT_Reports"
  RMDir /r "$DOCUMENTS\TLT_Backups"

  preserve_data:
SectionEnd

;--------------------------------
; Functions
Function .onInit
  ; Set default sections
  SectionSetFlags ${SecMain} 17        ; Required + Selected
  SectionSetFlags ${SecPython} 1       ; Selected
  SectionSetFlags ${SecDesktop} 1      ; Selected
  SectionSetFlags ${SecStartMenu} 1    ; Selected
  SectionSetFlags ${SecFileAssoc} 1    ; Selected
FunctionEnd

Function .onInstSuccess
  MessageBox MB_YESNO "Installation completed successfully!$\n$\n${APPNAME} has been installed with all modules:$\n• Customer Management$\n• Inventory Control$\n• Invoice Generation$\n• Supplier Management$\n• Purchase Management$\n• Sales Management$\n• Financial Reports$\n$\nWould you like to start ${APPNAME} now?" IDNO skip_run
  ExecShell "" "$INSTDIR\run_app.bat"
  skip_run:
FunctionEnd

Function un.onInit
  MessageBox MB_YESNO "Are you sure you want to remove ${APPNAME} and all its components?" IDYES +2
  Abort
FunctionEnd
