@echo off
title TLT Group Management System - Uninstaller
echo ========================================
echo TLT Group Management System Uninstaller
echo ========================================
echo.

REM Get the installation directory
set "INSTALL_DIR=%~dp0"
set "INSTALL_DIR=%INSTALL_DIR:~0,-1%"

echo Installation directory: %INSTALL_DIR%
echo.

echo WARNING: This will completely remove the TLT Group Management System
echo from your computer, including all configuration files.
echo.
echo Your invoice data and customer information will be preserved
echo in the Documents\TLT_Invoices folder.
echo.

set /p confirm="Are you sure you want to uninstall? (y/N): "
if /i not "%confirm%"=="y" (
    echo Uninstallation cancelled.
    pause
    exit /b 0
)

echo.
echo Starting uninstallation...
echo.

REM Stop any running instances
echo Stopping any running instances...
taskkill /f /im python.exe /fi "WINDOWTITLE eq TLT Group Management System*" >nul 2>&1
taskkill /f /im pythonw.exe /fi "WINDOWTITLE eq TLT Group Management System*" >nul 2>&1

REM Remove desktop shortcut
echo Removing desktop shortcut...
del "%USERPROFILE%\Desktop\TLT Group Management System.lnk" >nul 2>&1
del "%PUBLIC%\Desktop\TLT Group Management System.lnk" >nul 2>&1

REM Remove Start Menu shortcuts
echo Removing Start Menu shortcuts...
rmdir /s /q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\TLT Group Management System" >nul 2>&1
rmdir /s /q "%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\TLT Group Management System" >nul 2>&1

REM Remove Quick Launch shortcut
echo Removing Quick Launch shortcut...
del "%APPDATA%\Microsoft\Internet Explorer\Quick Launch\TLT Group Management System.lnk" >nul 2>&1

REM Remove registry entries (if any)
echo Cleaning registry entries...
reg delete "HKEY_CURRENT_USER\Software\TLT Group\Management System" /f >nul 2>&1
reg delete "HKEY_LOCAL_MACHINE\Software\TLT Group\Management System" /f >nul 2>&1

REM Remove from Add/Remove Programs
reg delete "HKEY_LOCAL_MACHINE\Software\Microsoft\Windows\CurrentVersion\Uninstall\TLT Group Management System" /f >nul 2>&1

REM Create a batch file to delete the installation directory after reboot
echo Creating cleanup script...
set "CLEANUP_SCRIPT=%TEMP%\tlt_cleanup.bat"
echo @echo off > "%CLEANUP_SCRIPT%"
echo timeout /t 3 /nobreak ^>nul >> "%CLEANUP_SCRIPT%"
echo rmdir /s /q "%INSTALL_DIR%" ^>nul 2^>^&1 >> "%CLEANUP_SCRIPT%"
echo del "%%~f0" ^>nul 2^>^&1 >> "%CLEANUP_SCRIPT%"

REM Schedule the cleanup script to run
echo Scheduling final cleanup...
start "" "%CLEANUP_SCRIPT%"

echo.
echo ========================================
echo Uninstallation completed successfully!
echo ========================================
echo.
echo The following items have been removed:
echo - Application files
echo - Desktop shortcuts
echo - Start Menu entries
echo - Registry entries
echo.
echo The following items have been preserved:
echo - Invoice data in Documents\TLT_Invoices
echo - Customer database backups (if any)
echo.
echo Thank you for using TLT Group Management System!
echo.

REM Ask if user wants to remove data as well
set /p remove_data="Do you also want to remove all invoice data? (y/N): "
if /i "%remove_data%"=="y" (
    echo.
    echo Removing invoice data...
    rmdir /s /q "%USERPROFILE%\Documents\TLT_Invoices" >nul 2>&1
    echo Invoice data removed.
) else (
    echo.
    echo Invoice data preserved in Documents\TLT_Invoices
)

echo.
echo You can now safely close this window.
pause

REM Exit the current process
exit /b 0
