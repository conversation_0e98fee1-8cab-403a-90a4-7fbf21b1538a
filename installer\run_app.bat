@echo off
title TLT Group Management System
cd /d "%~dp0"

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in PATH.
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

REM Check if required packages are installed
python -c "import tkinter, sqlite3, reportlab, PIL" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing required packages...
    python -m pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo Failed to install required packages.
        echo Please check your internet connection and try again.
        pause
        exit /b 1
    )
)

REM Run the application
echo Starting TLT Group Management System...
python main.py

REM If the application exits with an error, show the error
if %errorlevel% neq 0 (
    echo.
    echo Application exited with error code %errorlevel%
    pause
)
