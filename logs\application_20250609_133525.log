2025-06-09 13:35:25,657 - root - INFO - ===== TLT Group Management System Starting =====
2025-06-09 13:35:25,657 - root - INFO - Log file created at: C:\Users\<USER>\Desktop\music project\Accounting101\logs\application_20250609_133525.log
2025-06-09 13:35:25,707 - root - INFO - Searching for icon in: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-09 13:35:25,773 - root - INFO - Set icon using ICO file: C:\Users\<USER>\Desktop\music project\Accounting101\resources\TLT icon.ico
2025-06-09 13:35:25,795 - root - INFO - Found existing database at: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-09 13:35:25,796 - root - INFO - Selected database path: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-09 13:35:25,797 - root - INFO - Ensured database directory exists: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-09 13:35:25,799 - root - INFO - Database initialized successfully with WAL journal mode
2025-06-09 13:35:25,803 - root - INFO - Database check completed successfully
2025-06-09 13:35:25,806 - root - INFO - Loading module: Sales
2025-06-09 13:35:25,899 - root - INFO - Successfully loaded module: Sales
2025-06-09 13:35:25,899 - root - INFO - Loading module: Customers
2025-06-09 13:35:25,967 - root - INFO - Successfully loaded module: Customers
2025-06-09 13:35:25,968 - root - INFO - Loading module: Suppliers
2025-06-09 13:35:25,978 - modules.utils.table_customizer - INFO - Applied column order for suppliers: ['name', 'contact', 'address', 'balance']
2025-06-09 13:35:25,978 - modules.utils.table_customizer - INFO - Loaded saved column order for suppliers
2025-06-09 13:35:25,991 - root - INFO - Successfully loaded module: Suppliers
2025-06-09 13:35:25,992 - root - INFO - Loading module: Purchases
2025-06-09 13:35:26,038 - root - INFO - Successfully loaded module: Purchases
2025-06-09 13:35:26,038 - root - INFO - Loading module: Inventory
2025-06-09 13:35:26,076 - modules.utils.barcode_generator_new - INFO - Successfully imported PIL modules
2025-06-09 13:35:26,109 - modules.utils.barcode_generator_new - INFO - Successfully imported and tested barcode module
2025-06-09 13:35:26,251 - modules.utils.label_generator - INFO - Successfully imported reportlab modules
2025-06-09 13:35:26,303 - modules.utils.table_customizer - INFO - Applied column order for inventory: ['barcode', 'name', 'quantity', 'price', 'cost price', 'category', 'min stock', 'description']
2025-06-09 13:35:26,304 - modules.utils.table_customizer - INFO - Loaded saved column order for inventory
2025-06-09 13:35:26,306 - root - INFO - Successfully loaded module: Inventory
2025-06-09 13:35:26,306 - root - INFO - Loading module: Reports
2025-06-09 13:35:26,332 - root - INFO - Successfully loaded module: Reports
2025-06-09 13:35:26,333 - root - INFO - Loading module: Settings
2025-06-09 13:35:26,405 - root - INFO - Successfully loaded module: Settings
2025-06-09 13:35:26,405 - root - INFO - Application starting in unlocked state
2025-06-09 13:35:26,405 - root - INFO - Initializing security timer
2025-06-09 13:35:26,406 - root - INFO - Loaded security delay from config: 0 seconds
2025-06-09 13:35:26,406 - root - INFO - SecurityTimer initialized with delay: 0 seconds
2025-06-09 13:35:26,406 - root - INFO - Security timer initialized successfully
2025-06-09 13:35:26,406 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-09 13:35:35,559 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-09 13:35:35,559 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
2025-06-09 13:36:24,953 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-09 13:37:03,429 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-09 13:37:03,429 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
2025-06-09 13:37:11,459 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-09 13:37:14,055 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-09 13:37:14,056 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
2025-06-09 13:37:16,696 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-09 13:37:46,175 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-09 13:37:46,176 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
2025-06-09 13:37:47,317 - root - INFO - Changed to tab: Suppliers (index: 2)
2025-06-09 13:37:47,317 - root - INFO - Security delay is set to immediate, requiring password for Suppliers tab
2025-06-09 13:37:47,662 - root - INFO - Changed to tab: Purchases (index: 3)
2025-06-09 13:37:47,662 - root - INFO - Security delay is set to immediate, requiring password for Purchases tab
2025-06-09 13:37:48,390 - root - INFO - Changed to tab: Inventory (index: 4)
2025-06-09 13:37:48,390 - root - INFO - Security delay is set to immediate, requiring password for Inventory tab
2025-06-09 13:37:50,328 - root - INFO - Changed to tab: Reports (index: 5)
2025-06-09 13:37:50,328 - root - INFO - Security delay is set to immediate, requiring password for Reports tab
2025-06-09 13:37:50,992 - root - INFO - Changed to tab: Inventory (index: 4)
2025-06-09 13:37:50,993 - root - INFO - Security delay is set to immediate, requiring password for Inventory tab
2025-06-09 13:37:53,880 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-09 13:37:54,273 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-09 13:37:54,274 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
2025-06-09 13:38:00,735 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-09 13:38:10,814 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-09 13:38:10,814 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
2025-06-09 13:38:22,442 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-09 13:38:35,098 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-09 13:38:35,098 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
2025-06-09 13:39:08,039 - root - INFO - Application closing - performing cleanup
2025-06-09 13:39:08,091 - root - INFO - ===== TLT Group Management System Shutting Down =====
