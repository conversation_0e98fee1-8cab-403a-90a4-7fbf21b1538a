2025-06-08 21:56:34,782 - root - INFO - ===== TLT Group Management System Starting =====
2025-06-08 21:56:34,782 - root - INFO - Log file created at: C:\Users\<USER>\Desktop\music project\Accounting101\logs\application_20250608_215634.log
2025-06-08 21:56:34,836 - root - INFO - Searching for icon in: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-08 21:56:34,921 - root - INFO - Set icon using ICO file: C:\Users\<USER>\Desktop\music project\Accounting101\resources\TLT icon.ico
2025-06-08 21:56:34,992 - root - INFO - Found existing database at: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-08 21:56:34,994 - root - INFO - Selected database path: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-08 21:56:34,995 - root - INFO - Ensured database directory exists: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-08 21:56:35,021 - root - INFO - Database initialized successfully with WAL journal mode
2025-06-08 21:56:35,023 - root - INFO - Database check completed successfully
2025-06-08 21:56:35,025 - root - INFO - Loading module: Sales
2025-06-08 21:56:35,135 - root - INFO - Successfully loaded module: Sales
2025-06-08 21:56:35,135 - root - INFO - Loading module: Customers
2025-06-08 21:56:35,232 - root - INFO - Successfully loaded module: Customers
2025-06-08 21:56:35,233 - root - INFO - Loading module: Suppliers
2025-06-08 21:56:35,245 - modules.utils.table_customizer - INFO - Applied column order for suppliers: ['name', 'contact', 'address', 'balance']
2025-06-08 21:56:35,246 - modules.utils.table_customizer - INFO - Loaded saved column order for suppliers
2025-06-08 21:56:35,261 - root - INFO - Successfully loaded module: Suppliers
2025-06-08 21:56:35,261 - root - INFO - Loading module: Purchases
2025-06-08 21:56:35,309 - root - INFO - Successfully loaded module: Purchases
2025-06-08 21:56:35,310 - root - INFO - Loading module: Inventory
2025-06-08 21:56:35,363 - modules.utils.barcode_generator_new - INFO - Successfully imported PIL modules
2025-06-08 21:56:35,400 - modules.utils.barcode_generator_new - INFO - Successfully imported and tested barcode module
2025-06-08 21:56:35,559 - modules.utils.label_generator - INFO - Successfully imported reportlab modules
2025-06-08 21:56:35,613 - modules.utils.table_customizer - INFO - Applied column order for inventory: ['barcode', 'name', 'description', 'quantity', 'price', 'cost price', 'category', 'min stock']
2025-06-08 21:56:35,613 - modules.utils.table_customizer - INFO - Loaded saved column order for inventory
2025-06-08 21:56:35,616 - root - INFO - Successfully loaded module: Inventory
2025-06-08 21:56:35,616 - root - INFO - Loading module: Reports
2025-06-08 21:56:35,655 - root - INFO - Successfully loaded module: Reports
2025-06-08 21:56:35,655 - root - INFO - Loading module: Settings
2025-06-08 21:56:35,730 - root - INFO - Successfully loaded module: Settings
2025-06-08 21:56:35,730 - root - INFO - Application starting in unlocked state
2025-06-08 21:56:35,730 - root - INFO - Initializing security timer
2025-06-08 21:56:35,731 - root - INFO - Loaded security delay from config: 0 seconds
2025-06-08 21:56:35,731 - root - INFO - SecurityTimer initialized with delay: 0 seconds
2025-06-08 21:56:35,732 - root - INFO - Security timer initialized successfully
2025-06-08 21:56:35,732 - root - INFO - Changed to tab: Sales (index: 0)
