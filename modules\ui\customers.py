from .base_module import BaseModule
import tkinter as tk
from tkinter import ttk, messagebox
import os
import logging
from ..utils.table_customizer import TableCustomizer

logger = logging.getLogger(__name__)

class CustomersModule(BaseModule):
    def __init__(self, parent, db):
        super().__init__(parent, db)
        # Flag to track if refresh is needed
        self.needs_refresh = False
        self.statement_window = None
        self.selected_customer_id = None  # Track the currently selected customer ID

        # Create the interface
        self.create_customers_interface()

        # Set up auto-refresh
        self.refresh_interval = 60000  # 60 seconds
        self.setup_auto_refresh()

        # Setup invoice buttons
        self.setup_invoice_buttons()

    def create_customers_interface(self):
        # Main container with left and right panes
        main_frame = ttk.PanedWindow(self.frame, orient=tk.HORIZONTAL)
        main_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Left pane for customer list and form
        left_frame = ttk.Frame(main_frame)
        main_frame.add(left_frame, weight=1)

        # Right pane for customer invoices
        right_frame = ttk.Frame(main_frame)
        main_frame.add(right_frame, weight=1)

        # Add search and refresh functionality
        search_frame = ttk.Frame(left_frame)
        search_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(search_frame, text="Search:").pack(side="left", padx=5)
        self.search_var = tk.StringVar()
        self.search_var.trace_add('write', self.search_customers)
        ttk.Entry(search_frame, textvariable=self.search_var).pack(side="left", fill="x", expand=True)

        # Add refresh button
        ttk.Button(search_frame, text="Refresh", command=self.manual_refresh).pack(side="right", padx=5)

        # Customer entry form
        entry_frame = ttk.LabelFrame(left_frame, text="Add/Edit Customer")
        entry_frame.pack(fill="x", padx=5, pady=5)

        # Customer details
        fields = [("Name:", "name"), ("Contact:", "contact"), ("Address:", "address")]
        self.entries = {}

        for i, (label, key) in enumerate(fields):
            ttk.Label(entry_frame, text=label).grid(row=i, column=0, padx=5, pady=5)
            self.entries[key] = ttk.Entry(entry_frame, width=40)
            self.entries[key].grid(row=i, column=1, padx=5, pady=5)

        # Buttons
        button_frame = ttk.Frame(entry_frame)
        button_frame.grid(row=len(fields), column=0, columnspan=2, pady=10)

        ttk.Button(button_frame, text="Add Customer",
                  command=self.add_customer).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Update",
                  command=self.update_customer).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Clear",
                  command=self.clear_form).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Delete Selected",
                  command=self.delete_customer).pack(side="left", padx=5)

        # Customers list
        list_frame = ttk.LabelFrame(left_frame, text="Customers List")
        list_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Create tree view
        columns = ('name', 'contact', 'address', 'total_balance', 'unpaid_balance')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings')

        # Configure column headings
        headings = {
            'name': 'Name',
            'contact': 'Contact',
            'address': 'Address',
            'total_balance': 'Total Balance',
            'unpaid_balance': 'Unpaid Balance'
        }

        for col, heading in headings.items():
            self.tree.heading(col, text=heading)

        # Configure column widths
        self.tree.column('name', width=200, minwidth=150)
        self.tree.column('contact', width=150, minwidth=100)
        self.tree.column('address', width=250, minwidth=200)
        self.tree.column('total_balance', width=100, minwidth=80)
        self.tree.column('unpaid_balance', width=100, minwidth=80)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.tree.yview)
        scrollbar.pack(side="right", fill="y")
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(fill="both", expand=True, side="left")

        # Add column customization
        self.table_customizer = TableCustomizer(self.tree, "customers")

        # Bind selection event
        self.tree.bind("<<TreeviewSelect>>", self.on_customer_select)

        # Customer invoices section (right pane)
        invoice_frame = ttk.LabelFrame(right_frame, text="Customer Invoices")
        invoice_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Customer info section
        self.customer_info_frame = ttk.Frame(invoice_frame)
        self.customer_info_frame.pack(fill="x", padx=5, pady=5)

        self.customer_name_var = tk.StringVar(value="No customer selected")
        self.customer_balance_var = tk.StringVar(value="Balance: $0.00")

        # Customer info and buttons in a horizontal layout
        info_container = ttk.Frame(self.customer_info_frame)
        info_container.pack(fill="x", expand=True)

        # Left side - customer info
        info_left = ttk.Frame(info_container)
        info_left.pack(side="left", fill="x", expand=True)

        ttk.Label(info_left, textvariable=self.customer_name_var,
                 font=("Arial", 12, "bold")).pack(anchor="w", padx=5)
        ttk.Label(info_left, textvariable=self.customer_balance_var,
                 font=("Arial", 10)).pack(anchor="w", padx=5)

        # Right side - buttons
        info_right = ttk.Frame(info_container)
        info_right.pack(side="right", padx=5)

        # Add Account Statement button
        self.statement_button = ttk.Button(info_right, text="Account Statement",
                                         command=self.show_account_statement)
        self.statement_button.pack(side="right", padx=5)
        self.statement_button.state(["disabled"])  # Disabled until customer is selected

        # Invoices list
        columns = ('id', 'date', 'amount', 'status')
        self.invoices_tree = ttk.Treeview(invoice_frame, columns=columns, show='headings')

        # Configure column headings
        self.invoices_tree.heading('id', text='Invoice #')
        self.invoices_tree.heading('date', text='Date')
        self.invoices_tree.heading('amount', text='Amount')
        self.invoices_tree.heading('status', text='Status')

        # Configure column widths
        self.invoices_tree.column('id', width=80, minwidth=60)
        self.invoices_tree.column('date', width=100, minwidth=80)
        self.invoices_tree.column('amount', width=100, minwidth=80)
        self.invoices_tree.column('status', width=100, minwidth=80)

        # Configure tags for status colors
        self.invoices_tree.tag_configure('paid', foreground='green')
        self.invoices_tree.tag_configure('not_paid', foreground='red')

        # Add scrollbar
        invoice_scrollbar = ttk.Scrollbar(invoice_frame, orient="vertical", command=self.invoices_tree.yview)
        invoice_scrollbar.pack(side="right", fill="y")
        self.invoices_tree.configure(yscrollcommand=invoice_scrollbar.set)

        self.invoices_tree.pack(fill="both", expand=True, padx=5, pady=5)

        # Add column customization
        self.invoices_table_customizer = TableCustomizer(self.invoices_tree, "customer_invoices")

        # Invoice details section
        self.invoice_details_frame = ttk.LabelFrame(right_frame, text="Invoice Details")
        self.invoice_details_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Invoice items list
        columns = ('product', 'description', 'quantity', 'price', 'total')
        self.invoice_items_tree = ttk.Treeview(self.invoice_details_frame, columns=columns, show='headings')

        # Configure column headings
        self.invoice_items_tree.heading('product', text='Product')
        self.invoice_items_tree.heading('description', text='Description')
        self.invoice_items_tree.heading('quantity', text='Quantity')
        self.invoice_items_tree.heading('price', text='Price')
        self.invoice_items_tree.heading('total', text='Total')

        # Configure column widths
        self.invoice_items_tree.column('product', width=200, minwidth=150)
        self.invoice_items_tree.column('description', width=200, minwidth=150)
        self.invoice_items_tree.column('quantity', width=80, minwidth=60)
        self.invoice_items_tree.column('price', width=100, minwidth=80)
        self.invoice_items_tree.column('total', width=100, minwidth=80)

        # Add scrollbar
        items_scrollbar = ttk.Scrollbar(self.invoice_details_frame, orient="vertical", command=self.invoice_items_tree.yview)
        items_scrollbar.pack(side="right", fill="y")
        self.invoice_items_tree.configure(yscrollcommand=items_scrollbar.set)

        self.invoice_items_tree.pack(fill="both", expand=True, padx=5, pady=5)

        # Add column customization
        self.items_table_customizer = TableCustomizer(self.invoice_items_tree, "invoice_items")

        # Invoice summary section
        self.invoice_summary_frame = ttk.LabelFrame(self.invoice_details_frame, text="Invoice Summary")
        self.invoice_summary_frame.pack(fill="x", padx=5, pady=5)

        # Create a text widget for invoice summary
        self.invoice_summary_text = tk.Text(self.invoice_summary_frame, height=6, wrap=tk.WORD, state=tk.DISABLED)
        self.invoice_summary_text.pack(fill="x", padx=5, pady=5)

        # Add right-click menu for invoice status management
        self.invoice_menu = tk.Menu(self.invoices_tree, tearoff=0)
        self.invoice_menu.add_command(label="Print Invoice", command=self.print_selected_invoice)  # Add Print option
        self.invoice_menu.add_command(label="Edit Invoice", command=self.edit_selected_invoice)  # Add Edit option
        self.invoice_menu.add_separator()
        self.invoice_menu.add_command(label="Mark as Paid", command=lambda: self.update_invoice_status('Paid'))
        self.invoice_menu.add_command(label="Mark as Not Paid", command=lambda: self.update_invoice_status('Not Paid'))
        self.invoice_menu.add_separator()
        self.invoice_menu.add_command(label="Delete Invoice", command=self.delete_invoice, foreground='red')

        # Bind events
        self.invoices_tree.bind("<<TreeviewSelect>>", self.on_invoice_select)
        self.invoices_tree.bind("<Button-3>", self.show_invoice_menu)  # Right-click

        # Initialize the customer list
        self.manual_refresh()

    def add_customer(self):
        try:
            data = {key: self.entries[key].get().strip() for key in self.entries}
            if not data['name']:
                raise ValueError("Customer name is required")

            self.db.add_customer(data['name'], data['contact'], data['address'])
            self.clear_form()
            self.manual_refresh()  # Use manual refresh instead
            messagebox.showinfo("Success", "Customer added successfully")
        except Exception as e:
            messagebox.showerror("Error", str(e))

    def clear_form(self):
        for entry in self.entries.values():
            entry.delete(0, tk.END)

        # Note: We don't reset selected_customer_id here because this method is called
        # during customer selection to clear the form before populating it with new data

    def refresh_list(self):
        # Clear the tree
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Get customers with unpaid balance
        customers = self.db.get_customers()

        # Update the list
        self.update_customer_list(customers)

    def search_customers(self, *_):
        """Search customers by name or contact"""
        search_term = self.search_var.get().strip().lower()
        customers = self.db.get_customers()
        filtered = [c for c in customers if search_term in c[1].lower() or
                   (c[2] and search_term in c[2].lower())]
        self.update_customer_list(filtered)

    def update_customer_list(self, customers):
        """Update the customer list display"""
        self.tree.delete(*self.tree.get_children())
        for customer in customers:
            # Store the customer ID as a tag for later reference
            customer_id = customer[0]

            # Get total balance and unpaid balance
            total_balance = customer[4] if len(customer) > 4 else 0
            unpaid_balance = customer[5] if len(customer) > 5 else 0

            # Format balances as currency
            total_balance_str = f"${total_balance:.2f}" if total_balance else "$0.00"
            unpaid_balance_str = f"${unpaid_balance:.2f}" if unpaid_balance else "$0.00"

            # Insert with values excluding the ID column
            self.tree.insert('', 'end', values=(
                customer[1],  # name
                customer[2],  # contact
                customer[3],  # address
                total_balance_str,  # total balance
                unpaid_balance_str   # unpaid balance
            ), tags=(str(customer_id),))

    def delete_customer(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a customer to delete")
            return

        if messagebox.askyesno("Confirm", "Delete selected customer?"):
            # Require password confirmation for customer deletion
            from .login_dialog import check_password
            if not check_password(self.parent):
                messagebox.showinfo("Cancelled", "Customer deletion cancelled - password verification failed.")
                return

            try:
                item = self.tree.item(selected[0])
                # Get customer ID from tag
                customer_id = item['tags'][0] if item['tags'] else None
                if not customer_id:
                    messagebox.showerror("Error", "Could not find customer ID")
                    return

                self.db.delete_customer(int(customer_id))
                self.manual_refresh()  # Use manual refresh instead of just deleting the item
                messagebox.showinfo("Success", "Customer deleted successfully")
            except Exception as e:
                messagebox.showerror("Error", str(e))

    def update_customer(self):
        """Update existing customer"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a customer to update")
            return

        try:
            # Get data from form
            data = {key: self.entries[key].get().strip() for key in self.entries}
            if not data['name']:
                raise ValueError("Customer name is required")

            # Get customer ID from the selected item
            item = self.tree.item(selected[0])
            customer_id = item['tags'][0] if item['tags'] else None
            if not customer_id:
                raise ValueError("Could not identify customer")

            # Update customer in database
            self.db.update_customer(int(customer_id), data['name'], data['contact'], data['address'])

            # Clear form and refresh list
            self.clear_form()
            self.manual_refresh()  # Use manual refresh to maintain selection
            messagebox.showinfo("Success", "Customer updated successfully")
        except Exception as e:
            messagebox.showerror("Error", str(e))

    def on_customer_select(self, _):
        """Handle customer selection"""
        selected = self.tree.selection()
        if not selected:
            return

        # Get selected item values
        item = self.tree.item(selected[0])
        values = item['values']
        customer_id = item['tags'][0] if 'tags' in item and item['tags'] else None

        if not customer_id:
            return

        # Store the selected customer ID
        self.selected_customer_id = int(customer_id)

        # Clear form first
        self.clear_form()

        # Populate form with selected customer data
        self.entries['name'].insert(0, values[0])
        self.entries['contact'].insert(0, values[1] if values[1] else "")
        self.entries['address'].insert(0, values[2] if values[2] else "")

        # Update customer info display
        self.customer_name_var.set(values[0])

        # Show both total and unpaid balance
        total_balance = values[3].replace('$', '') if values[3] else "0.00"
        unpaid_balance = values[4].replace('$', '') if values[4] else "0.00"

        try:
            total_balance = float(total_balance)
            unpaid_balance = float(unpaid_balance)
            self.customer_balance_var.set(
                f"Total Balance: ${total_balance:.2f} | Unpaid: ${unpaid_balance:.2f}")
        except ValueError:
            self.customer_balance_var.set(f"Balance: {values[3]} | Unpaid: {values[4]}")

        # Load customer invoices
        self.load_customer_invoices(int(customer_id))

        # Enable the account statement button
        self.statement_button.state(["!disabled"])

    def on_invoice_select(self, _=None):
        """Handle invoice selection"""
        selected = self.invoices_tree.selection()
        if selected:
            # Enable/disable buttons based on payment status
            invoice_status = self.invoices_tree.item(selected[0])['values'][3]

            self.print_button.state(['!disabled'])  # Always enable print
            self.edit_button.state(['!disabled'])   # Always enable edit

            # Enable/disable status buttons based on current status
            if invoice_status == 'Not Paid':
                self.mark_paid_button.state(['!disabled'])
                self.mark_not_paid_button.state(['disabled'])
            else:  # Paid
                self.mark_paid_button.state(['disabled'])
                self.mark_not_paid_button.state(['!disabled'])

            # Load invoice items
            invoice_id = self.invoices_tree.item(selected[0])['values'][0]
            self.load_invoice_items(invoice_id)
        else:
            # Disable all buttons when no invoice is selected
            self.print_button.state(['disabled'])
            self.mark_paid_button.state(['disabled'])
            self.mark_not_paid_button.state(['disabled'])
            self.edit_button.state(['disabled'])

            # Clear invoice summary
            self.invoice_summary_text.config(state=tk.NORMAL)
            self.invoice_summary_text.delete(1.0, tk.END)
            self.invoice_summary_text.config(state=tk.DISABLED)

    def load_customer_invoices(self, customer_id):
        """Load invoices for the selected customer"""
        # Clear existing invoices
        for item in self.invoices_tree.get_children():
            self.invoices_tree.delete(item)

        # Clear invoice items
        for item in self.invoice_items_tree.get_children():
            self.invoice_items_tree.delete(item)

        try:
            # Get customer invoices
            invoices = self.db.get_customer_invoices(customer_id)

            if not invoices:
                print(f"No invoices found for customer #{customer_id}")
                return

            print(f"Loading {len(invoices)} invoices for customer #{customer_id}")

            # Add invoices to tree
            for invoice in invoices:
                try:
                    invoice_id = invoice[0]
                    date = invoice[3] if len(invoice) > 3 else "Unknown"
                    amount = invoice[2] if len(invoice) > 2 else 0.0

                    print(f"Processing invoice #{invoice_id}, date: {date}, amount: {amount}")

                    # Get status from database or use default
                    try:
                        # Check if status column exists in the result
                        status = None
                        for i, col in enumerate(self.db.cursor.description):
                            if col[0] == 'status':
                                status = invoice[i]
                                break

                        # If status not found in result, get it directly
                        if status is None:
                            status = self.db.get_invoice_status(invoice_id)

                        print(f"Invoice #{invoice_id} status: {status}")
                    except Exception as e:
                        print(f"Error getting invoice status: {e}")
                        status = "Not Paid"  # Default if error

                    # Determine tag based on status
                    tag = 'paid' if status == 'Paid' else 'not_paid'
                except Exception as e:
                    print(f"Error processing invoice: {e}")
                    continue

                self.invoices_tree.insert('', 'end', values=(
                    invoice_id,
                    date,
                    f"${amount:.2f}",
                    status
                ), tags=(tag,))

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load invoices: {str(e)}")

    def show_invoice_menu(self, event):
        """Show context menu for invoice management"""
        # Check if any item is selected
        selected = self.invoices_tree.identify_row(event.y)
        if not selected:
            return

        # Select the item under cursor
        self.invoices_tree.selection_set(selected)
        # Show the menu
        self.invoice_menu.post(event.x_root, event.y_root)

    def update_invoice_status(self, status):
        """Update the status of the selected invoice"""
        selected = self.invoices_tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select an invoice to update")
            return

        try:
            # Get invoice ID
            item = self.invoices_tree.item(selected[0])
            invoice_id = item['values'][0]

            # Update status in database
            success = self.db.update_invoice_status(invoice_id, status)

            if success:
                # Update the item in the tree
                values = list(item['values'])
                values[3] = status  # Status is at index 3

                # Determine tag based on status
                tag = 'paid' if status == 'Paid' else 'not_paid'

                self.invoices_tree.item(selected[0], values=tuple(values), tags=(tag,))

                # Refresh the customer list to update balances
                self.manual_refresh()

                messagebox.showinfo("Success", f"Invoice #{invoice_id} marked as {status}")
            else:
                messagebox.showerror("Error", "Failed to update invoice status")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to update invoice status: {str(e)}")

    def delete_invoice(self):
        """Delete the selected invoice"""
        selected = self.invoices_tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select an invoice to delete")
            return

        try:
            # Get invoice ID
            item = self.invoices_tree.item(selected[0])
            invoice_id = item['values'][0]

            # Print debug info
            print(f"Attempting to delete invoice with ID: {invoice_id}")
            print(f"Invoice values: {item['values']}")

            # Confirm deletion
            confirm = messagebox.askyesno("Confirm Deletion",
                                         f"Are you sure you want to delete Invoice #{invoice_id}?\n\nThis action cannot be undone!")
            if not confirm:
                return

            # Require password confirmation for invoice deletion
            from .login_dialog import check_password
            if not check_password(self.parent):
                messagebox.showinfo("Cancelled", "Invoice deletion cancelled - password verification failed.")
                return

            # Delete invoice from database
            try:
                self.db.delete_invoice(invoice_id)
                print(f"Successfully deleted invoice #{invoice_id} from database")

                # Remove from tree
                self.invoices_tree.delete(selected[0])

                # Clear invoice details
                for item in self.invoice_items_tree.get_children():
                    self.invoice_items_tree.delete(item)

                # Refresh the customer list to update balances
                self.manual_refresh()

                messagebox.showinfo("Success", f"Invoice #{invoice_id} has been deleted")
            except ValueError as ve:
                # Handle the case where the invoice is not found
                print(f"ValueError: {ve}")

                # Ask if the user wants to remove it from the UI anyway
                if messagebox.askyesno("Invoice Not Found",
                                     f"Invoice #{invoice_id} was not found in the database. \n\nDo you want to remove it from the list anyway?"):
                    # Remove from tree
                    self.invoices_tree.delete(selected[0])

                    # Clear invoice details
                    for item in self.invoice_items_tree.get_children():
                        self.invoice_items_tree.delete(item)

                    # Refresh the customer list to update balances
                    self.manual_refresh()

                    messagebox.showinfo("Success", f"Invoice #{invoice_id} has been removed from the list")

        except Exception as e:
            print(f"Error in delete_invoice: {e}")
            messagebox.showerror("Error", f"Failed to delete invoice: {str(e)}")

    def load_invoice_items(self, invoice_id):
        """Load invoice items into the treeview and display invoice summary"""
        try:
            # Clear existing items
            self.invoice_items_tree.delete(*self.invoice_items_tree.get_children())

            # Get items from database
            items = self.db.get_invoice_items(invoice_id)

            # Get invoice details for summary
            invoice = self.db.get_invoice_by_id(invoice_id)

            for item in items:
                try:
                    # Handle custom items vs inventory items
                    if item[9] is not None:  # Custom item (item_name field)
                        name = item[9]
                        description = item[10] if item[10] else ""
                    elif item[0] is not None:  # Regular inventory item
                        name = item[2]  # product name from inventory
                        description = item[8] if len(item) > 8 and item[8] else ""  # description from inventory
                    else:
                        # Fallback for any other case
                        name = "Unknown Product"
                        description = ""

                    # Handle quantity and price
                    quantity = float(item[3]) if item[3] is not None else 0  # Quantity from invoice_items

                    # Use price from invoice_items (index 11) if available, otherwise fallback to inventory price
                    if item[11] is not None:
                        price = float(item[11])
                    else:
                        price = float(item[4]) if item[4] is not None else 0

                    total = quantity * price

                    # Format the values properly - always show quantity as integer
                    formatted_quantity = str(int(quantity))
                    formatted_price = f"${price:.2f}"
                    formatted_total = f"${total:.2f}"

                    self.invoice_items_tree.insert('', 'end', values=(
                        name,
                        description,
                        formatted_quantity,
                        formatted_price,
                        formatted_total
                    ))

                except Exception as e:
                    print(f"Error processing item: {e}")

            # Update invoice summary
            self.update_invoice_summary(invoice_id, invoice)

        except Exception as e:
            print(f"Error loading invoice items: {e}")
            messagebox.showerror("Error", f"Failed to load invoice items: {str(e)}")

    def update_invoice_summary(self, invoice_id, invoice):
        """Update the invoice summary display with totals, VAT, discounts, and deposit information"""
        try:
            # Clear the summary text
            self.invoice_summary_text.config(state=tk.NORMAL)
            self.invoice_summary_text.delete(1.0, tk.END)

            if not invoice:
                self.invoice_summary_text.insert(tk.END, "No invoice details available")
                self.invoice_summary_text.config(state=tk.DISABLED)
                return

            # Calculate subtotal from items
            subtotal = 0
            for item in self.invoice_items_tree.get_children():
                values = self.invoice_items_tree.item(item)['values']
                total_str = str(values[4]).replace('$', '').replace(',', '')
                subtotal += float(total_str)

            # Get invoice details
            total_amount = float(invoice[2]) if len(invoice) > 2 and invoice[2] is not None else 0
            vat_rate = float(invoice[4]) if len(invoice) > 4 and invoice[4] is not None else 0
            vat_amount = float(invoice[5]) if len(invoice) > 5 and invoice[5] is not None else 0
            discount_amount = float(invoice[7]) if len(invoice) > 7 and invoice[7] is not None else 0
            discount_type = invoice[8] if len(invoice) > 8 and invoice[8] is not None else 'amount'
            deposit_amount = float(invoice[9]) if len(invoice) > 9 and invoice[9] is not None else 0
            deposit_date = invoice[10] if len(invoice) > 10 and invoice[10] is not None else None

            # Build summary text
            summary_lines = []
            summary_lines.append(f"Invoice #{invoice_id} Summary:")
            summary_lines.append("-" * 30)
            summary_lines.append(f"Subtotal: ${subtotal:.2f}")

            # Show discount if applicable
            if discount_amount > 0:
                if discount_type == 'percent':
                    summary_lines.append(f"Discount ({discount_amount}%): -${(subtotal * discount_amount / 100):.2f}")
                else:
                    summary_lines.append(f"Discount: -${discount_amount:.2f}")
                discounted_subtotal = subtotal - (subtotal * discount_amount / 100 if discount_type == 'percent' else discount_amount)
                summary_lines.append(f"After Discount: ${discounted_subtotal:.2f}")

            # Show VAT if applicable
            if vat_rate > 0:
                summary_lines.append(f"VAT ({vat_rate}%): ${vat_amount:.2f}")

            summary_lines.append(f"Total: ${total_amount:.2f}")

            # Show deposit information if applicable
            if deposit_amount > 0:
                if deposit_date:
                    summary_lines.append(f"Deposit Paid ({deposit_date}): ${deposit_amount:.2f}")
                else:
                    summary_lines.append(f"Deposit Paid: ${deposit_amount:.2f}")
                balance_due = total_amount - deposit_amount
                summary_lines.append(f"Balance Due: ${balance_due:.2f}")

            # Insert the summary text
            self.invoice_summary_text.insert(tk.END, "\n".join(summary_lines))
            self.invoice_summary_text.config(state=tk.DISABLED)

        except Exception as e:
            print(f"Error updating invoice summary: {e}")
            self.invoice_summary_text.config(state=tk.NORMAL)
            self.invoice_summary_text.delete(1.0, tk.END)
            self.invoice_summary_text.insert(tk.END, f"Error loading summary: {str(e)}")
            self.invoice_summary_text.config(state=tk.DISABLED)

    def manual_refresh(self):
        """Manually refresh the customer list"""
        try:
            print("Manually refreshing customer list...")

            # Remember the currently selected customer
            selected = self.tree.selection()
            selected_id = None
            if selected:
                item = self.tree.item(selected[0])
                if 'tags' in item and item['tags']:
                    selected_id = item['tags'][0]

            # Refresh the list
            self.refresh_list()

            # Restore the selection if possible
            if selected_id:
                for item_id in self.tree.get_children():
                    item = self.tree.item(item_id)
                    if 'tags' in item and item['tags'] and item['tags'][0] == selected_id:
                        self.tree.selection_set(item_id)
                        self.tree.see(item_id)  # Ensure the item is visible
                        break

            # Reset the refresh flag
            self.needs_refresh = False

        except Exception as e:
            print(f"Error during manual refresh: {e}")
            messagebox.showerror("Error", f"Failed to refresh customer list: {str(e)}")

    def refresh_data(self):
        """Auto-refresh the customer data"""
        try:
            print("Auto-refreshing customer data...")

            # Use the manual refresh method but don't show error messages
            # Remember the currently selected customer
            selected = self.tree.selection()
            selected_id = None
            if selected:
                item = self.tree.item(selected[0])
                if 'tags' in item and item['tags']:
                    selected_id = item['tags'][0]

            # Refresh the list
            self.refresh_list()

            # Restore the selection if possible
            if selected_id:
                for item_id in self.tree.get_children():
                    item = self.tree.item(item_id)
                    if 'tags' in item and item['tags'] and item['tags'][0] == selected_id:
                        self.tree.selection_set(item_id)
                        self.tree.see(item_id)  # Ensure the item is visible
                        break

            # If a customer is selected, also refresh their invoices
            if selected_id:
                self.load_customer_invoices(int(selected_id))

            # Reset the refresh flag
            self.needs_refresh = False

            print("Customer data refreshed successfully")
        except Exception as e:
            print(f"Error during auto-refresh: {e}")

    def setup_invoice_buttons(self):
        """Setup buttons for invoice actions"""
        button_frame = ttk.Frame(self.invoice_details_frame)
        button_frame.pack(fill="x", padx=5, pady=5)

        # Create custom style for Print button
        style = ttk.Style()
        style.configure('Print.TButton',
                       font=('Helvetica', 10, 'bold'),
                       padding=5,
                       background='#90EE90')

        # Add Print Invoice button with custom style
        self.print_button = ttk.Button(
            button_frame,
            text="Print Invoice",
            command=self.print_selected_invoice,
            style='Print.TButton'
        )
        self.print_button.pack(side="left", padx=5)

        # Add Paid button
        self.mark_paid_button = ttk.Button(
            button_frame,
            text="Paid",
            command=lambda: self.update_invoice_status('Paid')
        )
        self.mark_paid_button.pack(side="left", padx=5)

        # Add Not Paid button
        self.mark_not_paid_button = ttk.Button(
            button_frame,
            text="Not Paid",
            command=lambda: self.update_invoice_status('Not Paid')
        )
        self.mark_not_paid_button.pack(side="left", padx=5)

        # Create custom style for Edit button
        style.configure('Edit.TButton',
                       font=('Helvetica', 10),
                       padding=5,
                       background='#ADD8E6')  # Light blue background

        # Add Edit Invoice button
        self.edit_button = ttk.Button(
            button_frame,
            text="Edit Invoice",
            command=self.edit_selected_invoice,
            style='Edit.TButton'
        )
        self.edit_button.pack(side="left", padx=5)

        # Initially disable the buttons
        self.print_button.state(['disabled'])
        self.mark_paid_button.state(['disabled'])
        self.mark_not_paid_button.state(['disabled'])
        self.edit_button.state(['disabled'])

    def print_selected_invoice(self):
        """Print the selected invoice"""
        try:
            selected = self.invoices_tree.selection()
            if not selected:
                raise ValueError("Please select an invoice to print")

            invoice_id = self.invoices_tree.item(selected[0])['values'][0]

            # Get customer ID from the tree item tags
            selected_customer = self.tree.selection()
            if not selected_customer:
                raise ValueError("Please select a customer")

            customer_item = self.tree.item(selected_customer[0])
            if 'tags' in customer_item and customer_item['tags']:
                customer_id = int(customer_item['tags'][0])
            else:
                customer_id = int(customer_item['values'][0])

            # Get invoice items
            items = []
            for item in self.invoice_items_tree.get_children():
                values = self.invoice_items_tree.item(item)['values']

                # Handle quantity - convert to float and remove any trailing .0
                quantity = float(values[2])

                # Handle price - remove $ and convert to float
                price_str = str(values[3]).replace('$', '').replace(',', '')
                price = float(price_str)

                # Handle total - remove $ and convert to float
                total_str = str(values[4]).replace('$', '').replace(',', '')
                total = float(total_str)

                items.append({
                    'name': values[0],
                    'description': values[1] if values[1] else "",  # Handle empty description
                    'quantity': int(quantity),  # Convert to integer
                    'price': price,
                    'total': total
                })

            print(f"Debug - Items to print: {items}")  # Debug print
            print(f"Debug - Customer ID: {customer_id}")  # Debug print

            # Get current invoice status
            invoice_status = self.invoices_tree.item(selected[0])['values'][3]  # Status is the 4th column

            # Generate and open PDF
            filename = self.generate_invoice_pdf(invoice_id, customer_id, items)
            os.startfile(filename)

            # If the invoice is not already paid, ask if they want to mark it as paid
            if invoice_status == 'Not Paid':
                mark_as_paid = messagebox.askyesno(
                    "Mark as Paid",
                    "This invoice is currently marked as Not Paid. \nDo you want to mark it as Paid?"
                )

                if mark_as_paid:
                    # Mark invoice as Paid if user confirms
                    self.db.update_invoice_status(invoice_id, 'Paid')
                    # Update the invoice status in the tree view
                    self.invoices_tree.item(selected[0], values=(
                        self.invoices_tree.item(selected[0])['values'][0],  # Invoice ID
                        self.invoices_tree.item(selected[0])['values'][1],  # Date
                        self.invoices_tree.item(selected[0])['values'][2],  # Amount
                        'Paid'  # New status
                    ))
                    print(f"Debug - Invoice #{invoice_id} marked as Paid after printing")
            else:
                # Already paid, no need to ask
                print(f"Debug - Invoice #{invoice_id} already marked as Paid")

        except ValueError as ve:
            messagebox.showerror("Error", str(ve))
            print(f"Validation error: {ve}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to print invoice: {str(e)}")
            print(f"Error generating invoice: {e}")  # Debug print

    def edit_selected_invoice(self):
        """Edit the selected invoice in the Sales module"""
        selected = self.invoices_tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select an invoice to edit")
            return

        try:
            # Get invoice ID
            invoice_id = self.invoices_tree.item(selected[0])['values'][0]

            # Switch to Sales tab and load the invoice for editing
            self.open_invoice_in_sales_tab(invoice_id)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to open invoice for editing: {str(e)}")
            print(f"Error opening invoice for editing: {e}")

    def open_invoice_in_sales_tab(self, invoice_id):
        """Open the specified invoice in the Sales tab for editing"""
        try:
            # Import the get_main_window function to access the global main window instance
            from .main_window import get_main_window

            # Get the main window instance
            main_window = get_main_window()
            if not main_window:
                raise ValueError("Main window instance not found")

            # Check if the Sales module is available
            if 'Sales' not in main_window.modules:
                raise ValueError("Sales module not found in main window modules")

            # Get the Sales module
            sales_module = main_window.modules['Sales']

            # Find the Sales tab index
            sales_tab_index = None
            for i in range(main_window.notebook.index('end')):
                if main_window.notebook.tab(i, "text") == "Sales":
                    sales_tab_index = i
                    break

            if sales_tab_index is None:
                raise ValueError("Sales tab not found")

            # Switch to the Sales tab
            main_window.notebook.select(sales_tab_index)

            # Load the invoice in the Sales module
            if hasattr(sales_module, 'load_invoice_for_editing'):
                sales_module.load_invoice_for_editing(invoice_id)
            else:
                messagebox.showwarning("Warning", "The Sales module does not support invoice editing")

        except Exception as e:
            raise Exception(f"Failed to open invoice in Sales tab: {str(e)}")

    def generate_invoice_pdf(self, invoice_id, customer_id, items):
        """Generate PDF invoice"""
        from datetime import datetime
        import os

        try:
            # Debug print
            print(f"Debug - Generating invoice PDF for invoice #{invoice_id}, customer #{customer_id}")

            customer = self.db.get_customer_by_id(customer_id)
            if not customer:
                raise ValueError(f"Customer with ID {customer_id} not found")

            invoice = self.db.get_invoice_by_id(invoice_id)

            # Calculate totals
            subtotal = sum(item['total'] for item in items)

            # Set default values if invoice data is not available
            vat_rate = 0
            vat_amount = 0
            status = 'Not Paid'

            # Only try to get values from invoice if it exists
            if invoice:
                vat_rate = invoice[4] if len(invoice) > 4 else 0
                vat_amount = invoice[5] if len(invoice) > 5 else 0
                status = invoice[6] if len(invoice) > 6 else 'Not Paid'

            total_amount = subtotal + vat_amount

            # Get deposit amount and date if available
            deposit_amount = 0
            deposit_date = None
            if invoice and len(invoice) > 8:  # Check if deposit_amount column exists
                # Make sure deposit_amount is a float
                try:
                    deposit_amount = float(invoice[8]) if invoice[8] is not None else 0
                    print(f"Debug - Deposit value: {deposit_amount}, type: {type(deposit_amount)}")
                except (ValueError, TypeError) as e:
                    print(f"Debug - Error converting deposit to float: {e}, value: {invoice[8]}")
                    deposit_amount = 0

            if invoice and len(invoice) > 9:  # Check if deposit_date column exists
                deposit_date = invoice[9] if invoice[9] is not None else None
                print(f"Debug - Deposit date: {deposit_date}")

            # Prepare invoice data
            data = {
                'invoice_number': invoice_id,
                'date': datetime.now().strftime('%Y-%m-%d'),
                'customer_name': customer[1] if len(customer) > 1 else 'Unknown',
                'customer_contact': customer[2] if len(customer) > 2 else '',
                'customer_address': customer[3] if len(customer) > 3 else '',
                'items': items,
                'subtotal': subtotal,
                'vat_rate': vat_rate,
                'vat_amount': vat_amount,
                'deposit_amount': deposit_amount,
                'deposit_date': deposit_date,
                'total': total_amount,
                'balance_due': float(total_amount) - float(deposit_amount)  # Ensure both are floats
            }

            # Always use a directory in the user's Documents folder for invoices
            # This ensures we have write permissions even when running from an installed version
            invoices_dir = os.path.join(os.path.expanduser("~"), "Documents", "TLT_Invoices")

            # Ensure invoices directory exists
            try:
                os.makedirs(invoices_dir, exist_ok=True)
                print(f"Debug - Invoices directory created at: {invoices_dir}")
            except Exception as e:
                print(f"Debug - Error creating invoices directory: {e}")
                # If we can't create the directory in Documents, try a temp directory
                import tempfile
                invoices_dir = tempfile.gettempdir()
                print(f"Debug - Using temp directory as fallback: {invoices_dir}")

            # Base filename
            base_filename = f'invoice_{invoice_id}'

            # Check if this is an edited invoice
            version = 1
            while os.path.exists(os.path.join(invoices_dir, f'{base_filename}_v{version}.pdf')):
                version += 1

            # Generate filename with version number if needed
            if version > 1:
                filename = os.path.join(invoices_dir, f'{base_filename}_v{version}.pdf')
            else:
                filename = os.path.join(invoices_dir, f'{base_filename}.pdf')

            # Import the invoice generator function
            from ..utils.invoice_template import generate_custom_invoice

            # Generate invoice using custom template
            generate_custom_invoice(data, filename, {
                'currency': '$',
                'vat_rate': vat_rate,
                'status': status
            })

            return filename

        except Exception as e:
            print(f"Error in generate_invoice_pdf: {e}")
            raise Exception(f"Failed to generate invoice PDF: {str(e)}")

    def mark_invoice_as_paid(self):
        """Mark the selected invoice as paid"""
        try:
            selected = self.invoices_tree.selection()
            if not selected:
                raise ValueError("Please select an invoice")

            invoice_id = self.invoices_tree.item(selected[0])['values'][0]

            # Update the database
            self.db.mark_invoice_as_paid(invoice_id)

            # Update the display
            self.invoices_tree.item(selected[0], tags=('paid',))
            self.invoices_tree.item(selected[0], values=(
                *self.invoices_tree.item(selected[0])['values'][:3],
                'Paid'
            ))

            messagebox.showinfo("Success", "Invoice marked as paid")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to mark invoice as paid: {str(e)}")

    def show_account_statement(self):
        """Show the account statement for the selected customer"""
        if not self.selected_customer_id:
            messagebox.showinfo("No Customer Selected", "Please select a customer first.")
            return

        # Close any existing statement window
        if self.statement_window and self.statement_window.winfo_exists():
            self.statement_window.destroy()

        # Get customer statement data with detailed invoice information
        customer_info, transactions, invoice_details = self.db.get_customer_statement(self.selected_customer_id)

        if not customer_info:
            messagebox.showerror("Error", "Could not retrieve customer information.")
            return

        # Create a new window for the statement
        self.statement_window = tk.Toplevel(self.parent)
        self.statement_window.title(f"Account Statement - {customer_info[1]}")
        self.statement_window.geometry("1200x800")  # Increased size to 1200x800 for better fit
        self.statement_window.minsize(1100, 700)  # Increased minimum size
        self.statement_window.transient(self.parent)  # Set as transient to parent window
        self.statement_window.grab_set()  # Make window modal

        # Center the window on the screen
        self._center_window(self.statement_window)

        # Create main container frame with increased padding
        main_frame = ttk.Frame(self.statement_window, padding=15)
        main_frame.pack(fill="both", expand=True)

        # Store the current payment status filter
        self.payment_status_filter = tk.StringVar(value="All")

        # Customer information section with increased padding
        info_frame = ttk.LabelFrame(main_frame, text="Customer Information")
        info_frame.pack(fill="x", padx=10, pady=10)

        # Customer details
        customer_name = customer_info[1]
        customer_contact = customer_info[2] if customer_info[2] else "N/A"
        customer_address = customer_info[3] if customer_info[3] else "N/A"

        # Account summary (added to customer_info by the enhanced get_customer_statement method)
        total_invoiced = customer_info[4] if len(customer_info) > 4 else 0
        total_paid = customer_info[5] if len(customer_info) > 5 else 0
        total_unpaid = customer_info[6] if len(customer_info) > 6 else 0

        # Customer info section with increased padding
        customer_details = ttk.Frame(info_frame)
        customer_details.pack(side="left", fill="x", expand=True, padx=10, pady=10)

        # Increased font size and padding for better readability
        ttk.Label(customer_details, text=f"Name: {customer_name}", font=("Arial", 12, "bold")).pack(anchor="w", padx=10, pady=3)
        ttk.Label(customer_details, text=f"Contact: {customer_contact}", font=("Arial", 11)).pack(anchor="w", padx=10, pady=3)
        ttk.Label(customer_details, text=f"Address: {customer_address}", font=("Arial", 11)).pack(anchor="w", padx=10, pady=3)

        # Account summary section with increased padding
        account_summary = ttk.LabelFrame(info_frame, text="Account Summary")
        account_summary.pack(side="right", fill="x", padx=10, pady=10)

        # Increased font size and padding for better readability
        ttk.Label(account_summary, text=f"Total Invoiced: ${total_invoiced:.2f}",
                 font=("Arial", 12, "bold")).pack(anchor="w", padx=10, pady=3)
        ttk.Label(account_summary, text=f"Total Paid: ${total_paid:.2f}",
                 font=("Arial", 12, "bold"), foreground="green").pack(anchor="w", padx=10, pady=3)
        ttk.Label(account_summary, text=f"Total Unpaid: ${total_unpaid:.2f}",
                 font=("Arial", 12, "bold"), foreground="red").pack(anchor="w", padx=10, pady=3)

        # Filter section with increased padding
        filter_frame = ttk.LabelFrame(main_frame, text="Filters")
        filter_frame.pack(fill="x", padx=10, pady=10)

        filter_controls = ttk.Frame(filter_frame)
        filter_controls.pack(fill="x", padx=10, pady=10)

        # Combined filter row
        date_filter_frame = ttk.Frame(filter_controls)
        date_filter_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(date_filter_frame, text="Start Date:").grid(row=0, column=0, padx=5, pady=5)
        start_date_var = tk.StringVar()
        start_date_entry = ttk.Entry(date_filter_frame, textvariable=start_date_var, width=15)
        start_date_entry.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(date_filter_frame, text="End Date:").grid(row=0, column=2, padx=5, pady=5)
        end_date_var = tk.StringVar()
        end_date_entry = ttk.Entry(date_filter_frame, textvariable=end_date_var, width=15)
        end_date_entry.grid(row=0, column=3, padx=5, pady=5)

        def apply_filter():
            # Use the _refresh_statement method to apply all filters
            self._refresh_statement()

        ttk.Button(date_filter_frame, text="Apply Filters", command=apply_filter).grid(row=0, column=4, padx=5, pady=5)
        ttk.Button(date_filter_frame, text="Clear All Filters",
                  command=lambda: [start_date_var.set(''), end_date_var.set(''),
                                  self.payment_status_filter.set('All'), apply_filter()]
                 ).grid(row=0, column=5, padx=5, pady=5)

        # Add payment status filter to the same row
        ttk.Label(date_filter_frame, text="Payment Status:").grid(row=0, column=6, padx=(15, 5), pady=5)

        # Radio buttons for payment status filter
        ttk.Radiobutton(date_filter_frame, text="All", variable=self.payment_status_filter,
                       value="All").grid(row=0, column=7, padx=5, pady=5)
        ttk.Radiobutton(date_filter_frame, text="Paid", variable=self.payment_status_filter,
                       value="Paid").grid(row=0, column=8, padx=5, pady=5)
        ttk.Radiobutton(date_filter_frame, text="Not Paid", variable=self.payment_status_filter,
                       value="Not Paid").grid(row=0, column=9, padx=5, pady=5)

        # Add print options
        ttk.Separator(date_filter_frame, orient="vertical").grid(row=0, column=10, padx=10, pady=5, sticky="ns")

        # Checkbox for printing without details
        self.print_without_details = tk.BooleanVar(value=False)
        ttk.Checkbutton(date_filter_frame, text="Print Without Details",
                       variable=self.print_without_details).grid(row=0, column=11, padx=5, pady=5)

        # No separate apply button needed - using the combined Apply Filters button

        # Create a notebook for transactions and details with increased padding
        statement_notebook = ttk.Notebook(main_frame)
        statement_notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # Transactions tab
        transactions_frame = ttk.Frame(statement_notebook)
        statement_notebook.add(transactions_frame, text="Transactions")

        # Create treeview for transactions
        columns = ('date', 'type', 'invoice', 'amount', 'status', 'vat')
        statement_tree = ttk.Treeview(transactions_frame, columns=columns, show='headings')

        # Configure column headings
        statement_tree.heading('date', text='Date')
        statement_tree.heading('type', text='Type')
        statement_tree.heading('invoice', text='Invoice #')
        statement_tree.heading('amount', text='Amount')
        statement_tree.heading('status', text='Status')
        statement_tree.heading('vat', text='VAT')

        # Configure column widths - increased for larger window
        statement_tree.column('date', width=120, minwidth=100)
        statement_tree.column('type', width=100, minwidth=80)
        statement_tree.column('invoice', width=100, minwidth=80)
        statement_tree.column('amount', width=120, minwidth=100)
        statement_tree.column('status', width=100, minwidth=80)
        statement_tree.column('vat', width=150, minwidth=100)

        # Add scrollbar
        scrollbar = ttk.Scrollbar(transactions_frame, orient="vertical", command=statement_tree.yview)
        statement_tree.configure(yscrollcommand=scrollbar.set)

        # Pack treeview and scrollbar
        scrollbar.pack(side="right", fill="y")
        statement_tree.pack(side="left", fill="both", expand=True)

        # Configure tags for payment status
        statement_tree.tag_configure('paid', foreground='green')
        statement_tree.tag_configure('not_paid', foreground='red')

        # Add column customization
        # Store the customizer as an instance variable to prevent garbage collection
        self.statement_table_customizer = TableCustomizer(statement_tree, "customer_statement")

        # Invoice Details tab
        details_frame = ttk.Frame(statement_notebook)
        statement_notebook.add(details_frame, text="Invoice Details")

        # Create a frame for invoice selection
        invoice_select_frame = ttk.Frame(details_frame)
        invoice_select_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(invoice_select_frame, text="Select Invoice:").pack(side="left", padx=5)

        # Create a combobox for invoice selection
        invoice_ids = [str(t[0]) for t in transactions] if transactions else []
        invoice_var = tk.StringVar()
        invoice_cb = ttk.Combobox(invoice_select_frame, textvariable=invoice_var, values=invoice_ids, state="readonly")
        invoice_cb.pack(side="left", padx=5)

        # Create a frame for invoice details
        invoice_details_frame = ttk.LabelFrame(details_frame, text="Invoice Items")
        invoice_details_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Create treeview for invoice items
        item_columns = ('name', 'description', 'quantity', 'price', 'total')
        items_tree = ttk.Treeview(invoice_details_frame, columns=item_columns, show='headings')

        # Configure column headings
        items_tree.heading('name', text='Item')
        items_tree.heading('description', text='Description')
        items_tree.heading('quantity', text='Quantity')
        items_tree.heading('price', text='Price')
        items_tree.heading('total', text='Total')

        # Configure column widths - increased for larger window
        items_tree.column('name', width=200, minwidth=100)
        items_tree.column('description', width=300, minwidth=100)
        items_tree.column('quantity', width=100, minwidth=80)
        items_tree.column('price', width=120, minwidth=80)
        items_tree.column('total', width=120, minwidth=80)

        # Add scrollbar
        items_scrollbar = ttk.Scrollbar(invoice_details_frame, orient="vertical", command=items_tree.yview)
        items_tree.configure(yscrollcommand=items_scrollbar.set)

        # Pack treeview and scrollbar
        items_scrollbar.pack(side="right", fill="y")
        items_tree.pack(side="left", fill="both", expand=True)

        # Add column customization
        self.items_table_customizer = TableCustomizer(items_tree, "invoice_items_statement")

        # Function to update invoice details when an invoice is selected
        def update_invoice_details(_=None):
            selected_invoice = invoice_var.get()
            if selected_invoice:
                # Clear existing items
                for item in items_tree.get_children():
                    items_tree.delete(item)

                # Get items for the selected invoice
                invoice_id = int(selected_invoice)
                if invoice_id in invoice_details:
                    items = invoice_details[invoice_id]

                    # Add items to the treeview
                    for item in items:
                        name = item[0] if item[0] else "Unknown"
                        description = item[1] if item[1] else ""
                        quantity = int(item[2]) if item[2] is not None else 0
                        price = float(item[3]) if item[3] is not None else 0
                        total = float(item[4]) if item[4] is not None else 0

                        items_tree.insert('', 'end', values=(
                            name,
                            description,
                            quantity,
                            f"${price:.2f}",
                            f"${total:.2f}"
                        ))

        # Bind the combobox to update invoice details
        invoice_var.trace_add('write', update_invoice_details)

        # Bind double-click on transaction to show details
        def on_transaction_select(_=None):
            selected = statement_tree.selection()
            if selected:
                invoice_id = statement_tree.item(selected[0])['values'][2]  # Invoice # is the third column
                invoice_var.set(str(invoice_id))
                statement_notebook.select(1)  # Switch to the details tab

        statement_tree.bind('<Double-1>', on_transaction_select)

        # Populate the transactions
        self._update_statement_transactions(statement_tree, transactions)

        # Summary section with increased padding
        summary_frame = ttk.LabelFrame(main_frame, text="Summary")
        summary_frame.pack(fill="x", padx=10, pady=10)

        # Calculate totals
        total_amount = sum(float(t[2]) for t in transactions) if transactions else 0
        paid_amount = sum(float(t[2]) for t in transactions if t[3] == 'Paid') if transactions else 0
        unpaid_amount = sum(float(t[2]) for t in transactions if t[3] != 'Paid') if transactions else 0

        # Display summary with increased font size and padding
        ttk.Label(summary_frame, text=f"Total Transactions: {len(transactions)}",
                 font=("Arial", 12, "bold")).pack(side="left", padx=25, pady=8)
        ttk.Label(summary_frame, text=f"Total Amount: ${total_amount:.2f}",
                 font=("Arial", 12, "bold")).pack(side="left", padx=25, pady=8)
        ttk.Label(summary_frame, text=f"Paid: ${paid_amount:.2f}",
                 font=("Arial", 12, "bold"), foreground="green").pack(side="left", padx=25, pady=8)
        ttk.Label(summary_frame, text=f"Unpaid: ${unpaid_amount:.2f}",
                 font=("Arial", 12, "bold"), foreground="red").pack(side="left", padx=25, pady=8)

        # Buttons section with increased padding and larger buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill="x", padx=10, pady=15)

        # Style for larger buttons
        style = ttk.Style()
        style.configure('Large.TButton', font=('Arial', 11))

        ttk.Button(buttons_frame, text="Print Full Statement", style='Large.TButton',
                  command=lambda: self._print_statement(customer_info, transactions, use_filter=False)).pack(side="left", padx=10, pady=5)
        ttk.Button(buttons_frame, text="Print Custom", style='Large.TButton',
                  command=lambda: self._print_statement(customer_info, transactions, use_filter=True)).pack(side="left", padx=10, pady=5)
        ttk.Button(buttons_frame, text="Print Selected", style='Large.TButton',
                  command=self._print_selected_invoices).pack(side="left", padx=10, pady=5)
        ttk.Button(buttons_frame, text="Close", style='Large.TButton',
                  command=self.statement_window.destroy).pack(side="right", padx=10, pady=5)

        # Store reference to the statement tree for later use
        self.statement_tree = statement_tree

    def _update_statement_transactions(self, tree, transactions):
        """Update the statement transactions treeview"""
        # Clear existing items
        for item in tree.get_children():
            tree.delete(item)

        # Add transactions to the treeview
        for transaction in transactions:
            invoice_id = transaction[0]
            date = transaction[1]
            amount = float(transaction[2])
            status = transaction[3] if transaction[3] else "Not Paid"
            trans_type = transaction[4] if len(transaction) > 4 else "Invoice"

            # Get VAT information if available
            vat_rate = transaction[5] if len(transaction) > 5 and transaction[5] is not None else 0
            vat_amount = transaction[6] if len(transaction) > 6 and transaction[6] is not None else 0

            # Format VAT display
            vat_display = f"{vat_rate}% (${vat_amount:.2f})" if vat_rate > 0 else "N/A"

            # Determine tag based on status
            tag = 'paid' if status == 'Paid' else 'not_paid'

            # Insert the transaction
            tree.insert('', 'end', values=(
                date,
                trans_type,
                invoice_id,
                f"${amount:.2f}",
                status,
                vat_display
            ), tags=(tag,))

    def _refresh_statement(self):
        """Refresh the statement with the current filters"""
        if not hasattr(self, 'selected_customer_id') or not self.selected_customer_id:
            return

        # Get the current payment status filter
        payment_status = self.payment_status_filter.get()
        if payment_status == "All":
            payment_status = None  # No filter

        # Get date values if they exist
        start_date = ""
        end_date = ""

        # Find the date entry widgets in the statement window
        if hasattr(self, 'statement_window') and self.statement_window.winfo_exists():
            for widget in self.statement_window.winfo_children():
                if isinstance(widget, ttk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.LabelFrame) and child.winfo_children():
                            for grandchild in child.winfo_children():
                                if isinstance(grandchild, ttk.Frame):
                                    for entry in grandchild.winfo_children():
                                        if isinstance(entry, ttk.Entry):
                                            # Get the associated variable
                                            for var_name, var in vars(self).items():
                                                if isinstance(var, tk.StringVar) and var._name == entry._name:
                                                    if 'start_date' in var_name:
                                                        start_date = var.get().strip()
                                                    elif 'end_date' in var_name:
                                                        end_date = var.get().strip()

        # Validate dates (simple validation)
        if start_date and not self._is_valid_date(start_date):
            messagebox.showerror("Invalid Date", "Start date must be in YYYY-MM-DD format")
            return

        if end_date and not self._is_valid_date(end_date):
            messagebox.showerror("Invalid Date", "End date must be in YYYY-MM-DD format")
            return

        # Get filtered statement with invoice details
        customer_info, filtered_transactions, _ = self.db.get_customer_statement(
            self.selected_customer_id, start_date, end_date, payment_status)

        if not customer_info:
            messagebox.showerror("Error", "Could not retrieve customer information.")
            return

        # Find the statement tree in the window
        statement_tree = None
        if hasattr(self, 'statement_window') and self.statement_window.winfo_exists():
            for widget in self.statement_window.winfo_children():
                if isinstance(widget, ttk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.Notebook):
                            for tab in child.winfo_children():
                                for grandchild in tab.winfo_children():
                                    if isinstance(grandchild, ttk.Treeview):
                                        statement_tree = grandchild
                                        break

        if statement_tree:
            # Update the transactions display
            self._update_statement_transactions(statement_tree, filtered_transactions)

            # Update the invoice selection dropdown if it exists
            invoice_var = None
            invoice_cb = None
            for widget in self.statement_window.winfo_children():
                if isinstance(widget, ttk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.Notebook):
                            for tab in child.winfo_children():
                                for frame in tab.winfo_children():
                                    if isinstance(frame, ttk.Frame):
                                        for combobox in frame.winfo_children():
                                            if isinstance(combobox, ttk.Combobox):
                                                invoice_cb = combobox
                                                for var_name, var in vars(self).items():
                                                    if isinstance(var, tk.StringVar) and var._name == combobox._name:
                                                        invoice_var = var
                                                        break

            if invoice_var and invoice_cb:
                invoice_ids = [str(t[0]) for t in filtered_transactions] if filtered_transactions else []
                invoice_cb['values'] = invoice_ids
                invoice_var.set('')

    def _print_selected_invoices(self):
        """Print a statement containing only the selected invoices"""
        if not hasattr(self, 'statement_tree') or not self.selected_customer_id:
            messagebox.showinfo("No Selection", "Please select one or more invoices to print.")
            return

        # Get selected items
        selected_items = self.statement_tree.selection()
        if not selected_items:
            messagebox.showinfo("No Selection", "Please select one or more invoices to print.")
            return

        # Get customer info
        customer_info = self.db.get_customer_by_id(self.selected_customer_id)
        if not customer_info:
            messagebox.showerror("Error", "Could not retrieve customer information.")
            return

        # Extract selected transactions
        selected_transactions = []
        for item_id in selected_items:
            item_values = self.statement_tree.item(item_id, 'values')
            if item_values:
                # Find the corresponding transaction in the database
                invoice_id = item_values[2]  # Invoice # is the third column
                date = item_values[0]  # Date is the first column
                amount = float(item_values[3].replace('$', ''))  # Amount is the fourth column
                status = item_values[4]  # Status is the fifth column
                trans_type = item_values[1]  # Type is the second column

                # Create a transaction tuple similar to what comes from the database
                transaction = (invoice_id, date, amount, status, trans_type)
                selected_transactions.append(transaction)

        if not selected_transactions:
            messagebox.showinfo("No Valid Selections", "No valid invoices were selected.")
            return

        # Calculate totals for the selected transactions
        total_amount = sum(float(t[2]) for t in selected_transactions)
        paid_amount = sum(float(t[2]) for t in selected_transactions if t[3] == 'Paid')
        unpaid_amount = sum(float(t[2]) for t in selected_transactions if t[3] != 'Paid')

        # Add totals to customer_info
        customer_info_with_totals = list(customer_info) + [total_amount, paid_amount, unpaid_amount]

        # Check if we should print without details
        print_without_details = False
        if hasattr(self, 'print_without_details'):
            print_without_details = self.print_without_details.get()

        # Get invoice details for the selected invoices (if needed)
        invoice_details = {}
        if not print_without_details:
            invoice_ids = [t[0] for t in selected_transactions]

            # Fetch details for each selected invoice
            for invoice_id in invoice_ids:
                # Get items for this invoice
                self.db.cursor.execute("""
                    SELECT
                        ii.item_name,
                        ii.description,
                        ii.quantity,
                        ii.price,
                        (ii.quantity * ii.price) as item_total
                    FROM invoice_items ii
                    WHERE ii.invoice_id = ?
                    ORDER BY ii.id
                """, (invoice_id,))

                items = self.db.cursor.fetchall()
                invoice_details[invoice_id] = items

        # Print the statement with only the selected transactions and their details
        self._print_selected_statement(customer_info_with_totals, selected_transactions, invoice_details)

    def _print_selected_statement(self, customer_info, transactions, invoice_details):
        """Print a statement with the selected transactions and their details

        Args:
            customer_info: Customer information tuple with totals
            transactions: List of selected transactions
            invoice_details: Dictionary of invoice details for the selected invoices
        """
        # Check if we should print without details
        if hasattr(self, 'print_without_details') and self.print_without_details.get():
            # If printing without details, use an empty dictionary
            invoice_details = {}
        try:
            from datetime import datetime
            import os
            import tempfile
            from ..utils.invoice_template import generate_account_statement

            # Create a temporary file for the PDF
            temp_dir = tempfile.gettempdir()
            customer_name = customer_info[1].replace(' ', '_') if customer_info[1] else 'customer'

            # Add suffix to filename if printing without details
            is_summary = not invoice_details  # Empty invoice_details means no details
            suffix = "_summary" if is_summary else ""
            pdf_filename = os.path.join(temp_dir, f"{customer_name}_selected_statement{suffix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")

            # Generate the PDF using the invoice template
            try:
                # Set options for the statement generation
                options = {
                    'hide_transaction_count': is_summary  # Hide transaction count in summary view
                }

                # Generate the PDF with the selected transactions and their details
                pdf_file = generate_account_statement(customer_info, transactions, invoice_details, pdf_filename, options)

                # Open the PDF file
                if os.path.exists(pdf_file):
                    os.startfile(pdf_file)
                    statement_type = "Summary statement" if is_summary else "Detailed statement"
                    messagebox.showinfo("Account Statement", f"Selected invoices {statement_type.lower()} has been generated and opened.\n\nFile: {pdf_file}")
                else:
                    messagebox.showerror("Error", f"Failed to generate PDF file.")
            except Exception as pdf_error:
                print(f"Error generating PDF: {pdf_error}")
                messagebox.showerror("Error", f"Could not generate PDF: {str(pdf_error)}")

                # Fall back to text-based statement if PDF generation fails
                self._print_text_statement(customer_info, transactions, invoice_details)

        except Exception as e:
            print(f"Error printing selected statement: {e}")
            messagebox.showerror("Error", f"Could not print statement: {str(e)}")

    def _is_valid_date(self, date_str):
        """Simple date validation (YYYY-MM-DD)"""
        import re
        return bool(re.match(r'^\d{4}-\d{2}-\d{2}$', date_str))

    def _center_window(self, window):
        """Center a window on the screen"""
        window.update_idletasks()

        # Get the screen width and height
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()

        # Get the window size
        window_width = window.winfo_width()
        window_height = window.winfo_height()

        # Calculate the position
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # Set the position
        window.geometry(f"+{x}+{y}")

    def _print_statement(self, customer_info, transactions, use_filter=False):
        """Print the customer statement with detailed invoice information using the invoice design

        Args:
            customer_info: Customer information tuple
            transactions: List of transactions to include in the statement
            use_filter: Whether to use the current filter settings (True) or print all transactions (False)
        """
        try:
            from datetime import datetime
            import os
            import tempfile
            from ..utils.invoice_template import generate_account_statement

            # Get the current payment status filter if using filters
            payment_status = None
            if use_filter and hasattr(self, 'payment_status_filter'):
                status = self.payment_status_filter.get()
                if status != "All":
                    payment_status = status

            # Check if we should print without details
            print_without_details = False
            if hasattr(self, 'print_without_details'):
                print_without_details = self.print_without_details.get()

            # Get invoice details for the transactions (empty dict if printing without details)
            invoice_details = {}
            if not print_without_details:
                if use_filter:
                    # Use the current filtered transactions
                    _, _, invoice_details = self.db.get_customer_statement(
                        self.selected_customer_id, payment_status=payment_status)
                else:
                    # Get all transactions
                    _, _, invoice_details = self.db.get_customer_statement(self.selected_customer_id)

            # Create a temporary file for the PDF
            temp_dir = tempfile.gettempdir()
            customer_name = customer_info[1].replace(' ', '_') if customer_info[1] else 'customer'

            # Add suffix to filename if printing without details
            suffix = "_summary" if print_without_details else ""
            pdf_filename = os.path.join(temp_dir, f"{customer_name}_statement{suffix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")

            # Generate the PDF using the invoice template
            try:
                # Set options for the statement generation
                options = {
                    'hide_transaction_count': print_without_details  # Hide transaction count in summary view
                }

                # Generate the PDF with options
                pdf_file = generate_account_statement(customer_info, transactions, invoice_details, pdf_filename, options)

                # Open the PDF file
                if os.path.exists(pdf_file):
                    os.startfile(pdf_file)
                    statement_type = "Summary statement" if print_without_details else "Detailed statement"
                    messagebox.showinfo("Account Statement", f"{statement_type} has been generated and opened.\n\nFile: {pdf_file}")
                else:
                    messagebox.showerror("Error", f"Failed to generate PDF file.")
            except Exception as pdf_error:
                logger.error(f"Error generating PDF: {pdf_error}")
                messagebox.showerror("Error", f"Could not generate PDF: {str(pdf_error)}")

                # Fall back to text-based statement if PDF generation fails
                self._print_text_statement(customer_info, transactions, invoice_details)

        except Exception as e:
            logger.error(f"Error printing statement: {e}")
            messagebox.showerror("Error", f"Could not print statement: {str(e)}")

    def _print_text_statement(self, customer_info, transactions, invoice_details):
        """Fallback method to print a text-based statement if PDF generation fails"""
        try:
            from datetime import datetime

            # Create a simple text representation of the statement
            statement_text = []
            statement_text.append("CUSTOMER ACCOUNT STATEMENT")
            statement_text.append("=" * 80)
            statement_text.append(f"Date: {datetime.now().strftime('%Y-%m-%d')}")

            # Customer information
            statement_text.append("\nCUSTOMER INFORMATION:")
            statement_text.append("-" * 80)
            statement_text.append(f"Name: {customer_info[1]}")
            statement_text.append(f"Contact: {customer_info[2] if customer_info[2] else 'N/A'}")
            statement_text.append(f"Address: {customer_info[3] if customer_info[3] else 'N/A'}")

            # Account summary
            statement_text.append("\nACCOUNT SUMMARY:")
            statement_text.append("-" * 80)
            total_invoiced = customer_info[4] if len(customer_info) > 4 else 0
            total_paid = customer_info[5] if len(customer_info) > 5 else 0
            total_unpaid = customer_info[6] if len(customer_info) > 6 else 0
            statement_text.append(f"Total Invoiced: ${total_invoiced:.2f}")
            statement_text.append(f"Total Paid: ${total_paid:.2f}")
            statement_text.append(f"Total Unpaid: ${total_unpaid:.2f}")

            # Transactions list
            statement_text.append("\nTRANSACTIONS:")
            statement_text.append("-" * 80)
            statement_text.append(f"{'Date':<12} {'Type':<10} {'Invoice #':<10} {'Amount':>12} {'Status':<10} {'VAT':>15}")
            statement_text.append("-" * 80)

            for transaction in transactions:
                invoice_id = transaction[0]
                date = transaction[1]
                amount = float(transaction[2])
                status = transaction[3] if transaction[3] else "Not Paid"
                trans_type = transaction[4] if len(transaction) > 4 else "Invoice"

                # Get VAT information if available
                vat_rate = transaction[5] if len(transaction) > 5 and transaction[5] is not None else 0
                vat_amount = transaction[6] if len(transaction) > 6 and transaction[6] is not None else 0
                vat_display = f"{vat_rate}% (${vat_amount:.2f})" if vat_rate > 0 else "N/A"

                statement_text.append(f"{date:<12} {trans_type:<10} {invoice_id:<10} ${amount:>10.2f} {status:<10} {vat_display:>15}")

                # Add invoice details if available
                if invoice_id in invoice_details and invoice_details[invoice_id]:
                    statement_text.append(f"\n    Invoice #{invoice_id} Details:")
                    statement_text.append(f"    {'-' * 70}")
                    statement_text.append(f"    {'Item':<30} {'Quantity':>8} {'Price':>12} {'Total':>12}")
                    statement_text.append(f"    {'-' * 70}")

                    for item in invoice_details[invoice_id]:
                        name = item[0] if item[0] else "Unknown"
                        quantity = int(item[2]) if item[2] is not None else 0
                        price = float(item[3]) if item[3] is not None else 0
                        total = float(item[4]) if item[4] is not None else 0

                        statement_text.append(f"    {name:<30} {quantity:>8} ${price:>10.2f} ${total:>10.2f}")

                    statement_text.append(f"    {'-' * 70}")
                    statement_text.append(f"    {'Subtotal:':<40} ${amount-vat_amount:>10.2f}")
                    if vat_amount > 0:
                        statement_text.append(f"    {'VAT ('+ str(vat_rate) + '%):':<40} ${vat_amount:>10.2f}")
                    statement_text.append(f"    {'Total:':<40} ${amount:>10.2f}")
                    statement_text.append("")

            statement_text.append("-" * 80)
            statement_text.append(f"Total Transactions: {len(transactions)}")
            statement_text.append(f"Total Amount: ${total_invoiced:.2f}")
            statement_text.append(f"Paid Amount: ${total_paid:.2f}")
            statement_text.append(f"Unpaid Amount: ${total_unpaid:.2f}")

            # Save to a temporary file
            import tempfile
            with tempfile.NamedTemporaryFile(delete=False, mode='w', suffix='.txt') as f:
                f.write('\n'.join(statement_text))
                temp_filename = f.name

            # Open the file with the default text editor
            import os
            os.startfile(temp_filename, 'print')

            messagebox.showinfo("Print", "Statement has been sent to the printer.")

        except Exception as e:
            logger.error(f"Error printing text statement: {e}")
            messagebox.showerror("Error", f"Could not print text statement: {str(e)}")
