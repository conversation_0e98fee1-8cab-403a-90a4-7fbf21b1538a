@echo off
title Python Installation for TLT Group Management System
echo ========================================
echo Python Installation Helper
echo ========================================
echo.

REM Check if Python is already installed
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Python is already installed on this system.
    python --version
    echo.
    echo Checking if all required packages are available...
    python -c "import tkinter, sqlite3, reportlab, PIL" >nul 2>&1
    if %errorlevel% equ 0 (
        echo All required packages are available.
        echo You can proceed with running the TLT Group Management System.
    ) else (
        echo Some required packages are missing.
        echo Installing required packages...
        python -m pip install -r requirements.txt
    )
    pause
    exit /b 0
)

echo Python is not installed on this system.
echo.
echo The TLT Group Management System requires Python 3.8 or higher.
echo.
echo Options:
echo 1. Download and install Python automatically (recommended)
echo 2. Download Python manually
echo 3. Exit and install Python later
echo.
set /p choice="Please choose an option (1-3): "

if "%choice%"=="1" goto auto_install
if "%choice%"=="2" goto manual_install
if "%choice%"=="3" goto exit_installer
goto invalid_choice

:auto_install
echo.
echo Downloading Python installer...
echo.

REM Create temp directory
if not exist "%TEMP%\tlt_installer" mkdir "%TEMP%\tlt_installer"
cd /d "%TEMP%\tlt_installer"

REM Download Python installer using PowerShell
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe' -OutFile 'python_installer.exe'}"

if %errorlevel% neq 0 (
    echo Failed to download Python installer.
    echo Please check your internet connection and try again.
    echo.
    echo You can manually download Python from: https://python.org
    pause
    exit /b 1
)

echo.
echo Running Python installer...
echo.
echo IMPORTANT: Please make sure to check "Add Python to PATH" during installation!
echo.
pause

REM Run Python installer with recommended options
python_installer.exe /quiet InstallAllUsers=1 PrependPath=1 Include_test=0

if %errorlevel% equ 0 (
    echo.
    echo Python installation completed successfully!
    echo.
    echo Please restart this installer to continue with the TLT Group Management System installation.
) else (
    echo.
    echo Python installation may have failed or was cancelled.
    echo Please try installing Python manually from: https://python.org
)

REM Clean up
cd /d "%~dp0"
rmdir /s /q "%TEMP%\tlt_installer" >nul 2>&1

pause
exit /b 0

:manual_install
echo.
echo Please visit https://python.org to download and install Python 3.8 or higher.
echo.
echo Make sure to:
echo 1. Download the Windows x86-64 installer
echo 2. Check "Add Python to PATH" during installation
echo 3. Choose "Install for all users" if you have administrator rights
echo.
echo After installing Python, run this installer again.
echo.
start https://python.org/downloads/
pause
exit /b 0

:invalid_choice
echo.
echo Invalid choice. Please enter 1, 2, or 3.
echo.
goto auto_install

:exit_installer
echo.
echo Installation cancelled.
echo.
echo To use the TLT Group Management System, you will need to install Python first.
echo Visit https://python.org for download and installation instructions.
echo.
pause
exit /b 1
