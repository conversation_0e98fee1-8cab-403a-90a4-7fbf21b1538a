# TLT Group Management System - NSIS Installer Guide

## Overview
This guide explains how to create a professional Windows installer for the TLT Group Management System using NSIS (Nullsoft Scriptable Install System).

## Why NSIS?
- ✅ **Industry Standard**: Used by major software companies
- ✅ **Small File Size**: Creates compact installers
- ✅ **Professional**: Modern UI with wizard-style installation
- ✅ **Powerful**: Advanced scripting capabilities
- ✅ **Free**: Open source and completely free
- ✅ **Multi-language**: Built-in support for multiple languages

## Prerequisites

### 1. Install NSIS

#### Option A: Download from Official Website (Recommended)
1. Visit: https://nsis.sourceforge.io/Download
2. Download "NSIS 3.x" (latest stable version)
3. Run the installer and follow the setup wizard
4. Default installation path: `C:\Program Files (x86)\NSIS\`

#### Option B: Install via MSYS2 (For your environment)
```bash
# Open MSYS2 terminal
pacman -S mingw-w64-x86_64-nsis
```

#### Option C: Install via Chocolatey
```cmd
# If you have Chocolatey installed
choco install nsis
```

### 2. Verify Installation
```cmd
# Test NSIS installation
makensis /VERSION
```

## Building the Installer

### Method 1: Using Windows Batch Script
```cmd
# Navigate to installer directory
cd installer

# Run the build script
build_nsis.bat
```

### Method 2: Using MSYS2 Shell Script
```bash
# Navigate to installer directory
cd installer

# Make script executable and run
chmod +x build_nsis.sh
./build_nsis.sh
```

### Method 3: Manual Compilation
```cmd
# Navigate to installer directory
cd installer

# Compile directly with NSIS
makensis tlt_installer.nsi
```

## Installer Features

### 🎨 **Professional Interface**
- Modern UI with wizard-style installation
- Company branding with TLT Group logo
- Multi-language support (English/French)
- Progress bars and status updates

### 🔧 **Smart Installation**
- Automatic Python detection and dependency installation
- System requirements checking
- Registry integration for Add/Remove Programs
- Proper file associations and shortcuts

### 📁 **Complete Package Management**
- Installs all application files and dependencies
- Creates desktop and Start Menu shortcuts
- Sets up proper directory structure
- Configures application settings

### 🛡️ **Security & Reliability**
- Administrator privilege handling
- Clean uninstallation process
- Registry cleanup
- File integrity verification

## Installer Components

### Core Files Included
```
TLT_Group_Management_System_Setup.exe
├── Application Files
│   ├── main.py (Main application)
│   ├── requirements.txt (Python dependencies)
│   ├── modules/ (All Python modules)
│   ├── resources/ (Icons, images, etc.)
│   └── config/ (Configuration files)
├── Launcher
│   ├── run_app.bat (Application launcher)
│   └── system_check.py (System verification)
└── Documentation
    ├── README.md (User guide)
    └── License agreement
```

### Installation Options
1. **Main Application** (Required)
   - Core application files
   - Python modules and resources
   - Configuration files

2. **Desktop Shortcut** (Optional)
   - Creates desktop icon for easy access
   - Uses company logo as icon

3. **Start Menu Shortcuts** (Optional)
   - Application launcher
   - System check utility
   - Uninstaller shortcut

4. **Python Dependencies** (Recommended)
   - Automatically installs required Python packages
   - Handles pip installation and updates

## Installation Process

### For End Users
1. **Download** the installer file
2. **Right-click** → "Run as administrator"
3. **Follow the wizard**:
   - Welcome screen
   - License agreement
   - Component selection
   - Installation directory
   - Start Menu folder
   - Installation progress
   - Completion

### Automatic Features
- **Python Check**: Verifies Python 3.8+ is installed
- **Dependency Installation**: Runs `pip install -r requirements.txt`
- **Shortcut Creation**: Desktop and Start Menu shortcuts
- **Registry Integration**: Adds to Windows Add/Remove Programs
- **File Associations**: Sets up proper file handling

## Customization Options

### Modifying the Installer
Edit `tlt_installer.nsi` to customize:

#### Change Installation Directory
```nsis
InstallDir "$PROGRAMFILES64\Your Company\Your App"
```

#### Add Custom Components
```nsis
Section "Custom Component" SecCustom
  ; Your custom installation code here
SectionEnd
```

#### Modify Shortcuts
```nsis
CreateShortcut "$DESKTOP\Your App.lnk" \
               "$INSTDIR\your_app.exe" \
               "" \
               "$INSTDIR\icon.ico"
```

#### Add Registry Entries
```nsis
WriteRegStr HKLM "Software\Your Company\Your App" \
                 "InstallPath" "$INSTDIR"
```

## Advanced Features

### Code Signing (Recommended for Production)
```cmd
# Sign the installer (requires code signing certificate)
signtool sign /f certificate.pfx /p password TLT_Group_Management_System_Setup.exe
```

### Custom Branding
- Replace `TLT icon.ico` with your company logo
- Modify colors and fonts in the NSI script
- Add custom graphics and splash screens

### Multi-language Support
The installer already includes English and French. To add more languages:
```nsis
!insertmacro MUI_LANGUAGE "German"
!insertmacro MUI_LANGUAGE "Spanish"
```

## Troubleshooting

### Common Build Issues

**"makensis: command not found"**
- NSIS is not installed or not in PATH
- Install NSIS and restart command prompt

**"Error in script"**
- Check NSI file syntax
- Verify all file paths exist
- Ensure proper NSIS syntax

**"Access denied"**
- Run command prompt as Administrator
- Check file permissions
- Disable antivirus temporarily

### Common Installation Issues

**"Python not found"**
- Install Python 3.8+ from python.org
- Ensure "Add Python to PATH" is checked during Python installation

**"Permission denied during installation"**
- Run installer as Administrator
- Check Windows UAC settings
- Verify user has installation privileges

**"Dependencies failed to install"**
- Check internet connection
- Verify pip is working: `pip --version`
- Install dependencies manually: `pip install -r requirements.txt`

## File Sizes and Performance

### Typical Sizes
- **Installer**: 15-30 MB (depending on included components)
- **Installed Application**: 50-100 MB
- **With Dependencies**: 100-200 MB (including Python packages)

### Optimization Tips
- Use LZMA compression for smaller file size
- Exclude unnecessary files from installation
- Consider separate dependency installer for large packages

## Distribution

### Testing Checklist
- [ ] Test on clean Windows 10/11 system
- [ ] Verify Python installation detection
- [ ] Check all shortcuts work correctly
- [ ] Test uninstallation process
- [ ] Verify Add/Remove Programs entry

### Distribution Methods
1. **Website Download**: Host on company website
2. **Email Distribution**: Send to customers (check size limits)
3. **USB/CD Distribution**: Copy to removable media
4. **Network Deployment**: Use for corporate installations

## Security Considerations

### Best Practices
- Code sign the installer for production use
- Provide SHA256 checksums for verification
- Host downloads on secure HTTPS servers
- Consider antivirus whitelisting for corporate environments

### User Trust
- Signed installers show publisher information
- Reduces Windows security warnings
- Increases user confidence in installation

---

## Quick Start Summary

1. **Install NSIS**: Download from official website or use `pacman -S mingw-w64-x86_64-nsis`
2. **Navigate to installer directory**: `cd installer`
3. **Run build script**: `build_nsis.bat` or `./build_nsis.sh`
4. **Find installer**: `output/TLT_Group_Management_System_Setup.exe`
5. **Test installer**: Run on clean system to verify functionality
6. **Distribute**: Share the installer file with end users

**TLT Group SARL**  
© 2025 All Rights Reserved
