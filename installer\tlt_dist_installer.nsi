;===============================================================================
;TLT Group Management System - NSIS Installer from Dist
;Created for TLT Group SARL
;Version: 3.0.0 - Complete Business Management Solution
;Build Date: January 2025
;===============================================================================

;--------------------------------
;Include Modern UI and Required Libraries
!include "MUI2.nsh"
!include "FileFunc.nsh"
!include "LogicLib.nsh"
!include "WinVer.nsh"
!include "x64.nsh"

;--------------------------------
;General Application Information
!define APPNAME "TLT Group Management System"
!define COMPANYNAME "TLT Group SARL"
!define DESCRIPTION "Complete Business Management Solution"
!define VERSIONMAJOR 3
!define VERSIONMINOR 0
!define VERSIONBUILD 0
!define VERSION "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}"
!define HELPURL "https://tltgroup.com/support"
!define UPDATEURL "https://tltgroup.com/updates"
!define ABOUTURL "https://tltgroup.com"

;--------------------------------
;Installer Settings
Name "${APPNAME}"
OutFile "TLT_Group_Management_System_Complete_Setup_v${VERSION}.exe"
Unicode True
BrandingText "${COMPANYNAME} - ${DESCRIPTION}"

;Default installation folder
InstallDir "$PROGRAMFILES64\TLT Group\Management System"

;Get installation folder from registry if available
InstallDirRegKey HKLM "Software\${COMPANYNAME}\${APPNAME}" "InstallLocation"

;Request application privileges
RequestExecutionLevel admin

;Compression settings
SetCompressor /SOLID lzma
SetCompressorDictSize 32

;--------------------------------
;Variables
Var StartMenuFolder

;--------------------------------
;Interface Settings
!define MUI_ABORTWARNING
!define MUI_ICON "..\resources\TLT icon.ico"
!define MUI_UNICON "..\resources\TLT icon.ico"
!define MUI_HEADERIMAGE
!define MUI_HEADERIMAGE_BITMAP "..\resources\TLT icon.ico"
!define MUI_WELCOMEFINISHPAGE_BITMAP "..\resources\TLT icon.ico"
!define MUI_UNWELCOMEFINISHPAGE_BITMAP "..\resources\TLT icon.ico"

;Welcome Page
!define MUI_WELCOMEPAGE_TITLE "Welcome to ${APPNAME} Setup"
!define MUI_WELCOMEPAGE_TEXT "This wizard will guide you through the installation of ${APPNAME}.$\r$\n$\r$\nThis powerful business management solution includes:$\r$\n• Customer Management$\r$\n• Inventory Control$\r$\n• Invoice Generation$\r$\n• Financial Reports$\r$\n• Supplier Management$\r$\n$\r$\nClick Next to continue."

;License Page
!define MUI_LICENSEPAGE_TEXT_TOP "Please review the license terms before installing ${APPNAME}."
!define MUI_LICENSEPAGE_TEXT_BOTTOM "If you accept the terms of the agreement, click I Agree to continue. You must accept the agreement to install ${APPNAME}."

;Components Page
!define MUI_COMPONENTSPAGE_TEXT_TOP "Select the components you want to install and clear the components you do not want to install. Click Next to continue."

;Directory Page
!define MUI_DIRECTORYPAGE_TEXT_TOP "Setup will install ${APPNAME} in the following folder. To install in a different folder, click Browse and select another folder. Click Next to continue."

;Start Menu Page
!define MUI_STARTMENUPAGE_DEFAULTFOLDER "${APPNAME}"
!define MUI_STARTMENUPAGE_REGISTRY_ROOT "HKLM"
!define MUI_STARTMENUPAGE_REGISTRY_KEY "Software\${COMPANYNAME}\${APPNAME}"
!define MUI_STARTMENUPAGE_REGISTRY_VALUENAME "Start Menu Folder"

;Finish Page
!define MUI_FINISHPAGE_TITLE "Completing the ${APPNAME} Setup Wizard"
!define MUI_FINISHPAGE_TEXT "${APPNAME} has been installed on your computer.$\r$\n$\r$\nClick Finish to close this wizard."
!define MUI_FINISHPAGE_RUN "$INSTDIR\TLT Group Management System.exe"
!define MUI_FINISHPAGE_RUN_TEXT "Start ${APPNAME}"
!define MUI_FINISHPAGE_SHOWREADME "$INSTDIR\readme.txt"
!define MUI_FINISHPAGE_SHOWREADME_TEXT "Show installation notes"

;Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "license.txt"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_STARTMENU Application $StartMenuFolder
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

;Uninstaller Pages
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

;--------------------------------
;Languages
!insertmacro MUI_LANGUAGE "English"
!insertmacro MUI_LANGUAGE "French"
!insertmacro MUI_LANGUAGE "Arabic"

;--------------------------------
;Version Information
VIProductVersion "${VERSION}.0"
VIAddVersionKey /LANG=${LANG_ENGLISH} "ProductName" "${APPNAME}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "CompanyName" "${COMPANYNAME}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "LegalCopyright" "© ${COMPANYNAME}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "FileDescription" "${DESCRIPTION}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "FileVersion" "${VERSION}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "ProductVersion" "${VERSION}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "InternalName" "${APPNAME}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "OriginalFilename" "TLT_Group_Management_System_Complete_Setup_v${VERSION}.exe"

;--------------------------------
;Installer Sections

Section "!${APPNAME} (Required)" SecMain
  SectionIn RO
  
  DetailPrint "Installing ${APPNAME} application files..."
  
  ; Set output path to the installation directory
  SetOutPath $INSTDIR
  
  ; Copy the entire dist folder contents
  File /r "..\dist\TLT Group Management System\*.*"
  
  ; Copy additional files
  File "readme.txt"
  File "system_check.py"
  
  ; Create necessary directories
  DetailPrint "Creating application directories..."
  CreateDirectory "$INSTDIR\logs"
  CreateDirectory "$INSTDIR\backups"
  CreateDirectory "$INSTDIR\reports"
  CreateDirectory "$INSTDIR\invoices"
  CreateDirectory "$INSTDIR\labels"
  CreateDirectory "$INSTDIR\barcodes"
  
  ; Create user data directories
  CreateDirectory "$DOCUMENTS\TLT_Invoices"
  CreateDirectory "$DOCUMENTS\TLT_Reports"
  CreateDirectory "$DOCUMENTS\TLT_Backups"
  
  ; Store installation information in registry
  DetailPrint "Registering application..."
  WriteRegStr HKLM "Software\${COMPANYNAME}\${APPNAME}" "InstallLocation" $INSTDIR
  WriteRegStr HKLM "Software\${COMPANYNAME}\${APPNAME}" "Version" "${VERSION}"
  
  ; Create uninstaller
  DetailPrint "Creating uninstaller..."
  WriteUninstaller "$INSTDIR\Uninstall.exe"
  
  ; Add to Add/Remove Programs
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" \
                   "DisplayName" "${APPNAME}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" \
                   "UninstallString" "$\"$INSTDIR\Uninstall.exe$\""
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" \
                   "QuietUninstallString" "$\"$INSTDIR\Uninstall.exe$\" /S"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" \
                   "InstallLocation" "$INSTDIR"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" \
                   "DisplayIcon" "$INSTDIR\TLT Group Management System.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" \
                   "Publisher" "${COMPANYNAME}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" \
                   "DisplayVersion" "${VERSION}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" \
                   "URLInfoAbout" "${ABOUTURL}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" \
                   "URLUpdateInfo" "${UPDATEURL}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" \
                   "HelpLink" "${HELPURL}"
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" \
                     "NoModify" 1
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" \
                     "NoRepair" 1
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" \
                     "VersionMajor" ${VERSIONMAJOR}
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" \
                     "VersionMinor" ${VERSIONMINOR}
  
  ; Calculate and store installation size
  ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
  IntFmt $0 "0x%08X" $0
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" \
                     "EstimatedSize" "$0"

SectionEnd

Section "Desktop Shortcut" SecDesktop
  DetailPrint "Creating desktop shortcut..."
  CreateShortcut "$DESKTOP\${APPNAME}.lnk" \
                 "$INSTDIR\TLT Group Management System.exe" \
                 "" \
                 "$INSTDIR\TLT Group Management System.exe" \
                 0 \
                 SW_SHOWNORMAL \
                 "" \
                 "${APPNAME} - ${DESCRIPTION}"
SectionEnd

Section "Start Menu Shortcuts" SecStartMenu
  DetailPrint "Creating Start Menu shortcuts..."
  !insertmacro MUI_STARTMENU_WRITE_BEGIN Application

    CreateDirectory "$SMPROGRAMS\$StartMenuFolder"

    ; Main application shortcut
    CreateShortcut "$SMPROGRAMS\$StartMenuFolder\${APPNAME}.lnk" \
                   "$INSTDIR\TLT Group Management System.exe" \
                   "" \
                   "$INSTDIR\TLT Group Management System.exe" \
                   0 \
                   SW_SHOWNORMAL \
                   "" \
                   "${APPNAME}"

    ; Documentation
    CreateShortcut "$SMPROGRAMS\$StartMenuFolder\User Guide.lnk" \
                   "$INSTDIR\readme.txt" \
                   "" \
                   "$INSTDIR\TLT Group Management System.exe" \
                   0 \
                   SW_SHOWNORMAL \
                   "" \
                   "User Guide and Documentation"

    ; Uninstaller
    CreateShortcut "$SMPROGRAMS\$StartMenuFolder\Uninstall ${APPNAME}.lnk" \
                   "$INSTDIR\Uninstall.exe" \
                   "" \
                   "$INSTDIR\TLT Group Management System.exe" \
                   0 \
                   SW_SHOWNORMAL \
                   "" \
                   "Uninstall ${APPNAME}"

  !insertmacro MUI_STARTMENU_WRITE_END
SectionEnd

Section "File Associations" SecFileAssoc
  DetailPrint "Setting up file associations..."

  ; Associate .tlt files with the application
  WriteRegStr HKCR ".tlt" "" "TLTGroupFile"
  WriteRegStr HKCR "TLTGroupFile" "" "TLT Group Data File"
  WriteRegStr HKCR "TLTGroupFile\DefaultIcon" "" "$INSTDIR\TLT Group Management System.exe"
  WriteRegStr HKCR "TLTGroupFile\shell\open\command" "" '"$INSTDIR\TLT Group Management System.exe" "%1"'

  ; Associate .tltbackup files
  WriteRegStr HKCR ".tltbackup" "" "TLTGroupBackup"
  WriteRegStr HKCR "TLTGroupBackup" "" "TLT Group Backup File"
  WriteRegStr HKCR "TLTGroupBackup\DefaultIcon" "" "$INSTDIR\TLT Group Management System.exe"
  WriteRegStr HKCR "TLTGroupBackup\shell\open\command" "" '"$INSTDIR\TLT Group Management System.exe" "%1"'

  ; Refresh shell
  System::Call 'shell32.dll::SHChangeNotify(l, l, i, i) v (0x08000000, 0, 0, 0)'
SectionEnd

;--------------------------------
;Section Descriptions

;English descriptions
LangString DESC_SecMain ${LANG_ENGLISH} "Core application files and modules (required). Complete standalone application with all dependencies included."
LangString DESC_SecDesktop ${LANG_ENGLISH} "Creates a desktop shortcut for quick access to ${APPNAME}."
LangString DESC_SecStartMenu ${LANG_ENGLISH} "Creates Start Menu shortcuts including application launcher and documentation."
LangString DESC_SecFileAssoc ${LANG_ENGLISH} "Associates TLT file types with the application for seamless file handling."

;French descriptions
LangString DESC_SecMain ${LANG_FRENCH} "Fichiers principaux et modules de l'application (requis). Application autonome complète avec toutes les dépendances incluses."
LangString DESC_SecDesktop ${LANG_FRENCH} "Crée un raccourci sur le bureau pour un accès rapide à ${APPNAME}."
LangString DESC_SecStartMenu ${LANG_FRENCH} "Crée des raccourcis dans le menu Démarrer incluant le lanceur d'application et la documentation."
LangString DESC_SecFileAssoc ${LANG_FRENCH} "Associe les types de fichiers TLT avec l'application pour une gestion transparente des fichiers."

;Arabic descriptions
LangString DESC_SecMain ${LANG_ARABIC} "ملفات التطبيق الأساسية والوحدات (مطلوب). تطبيق مستقل كامل مع جميع التبعيات المدرجة."
LangString DESC_SecDesktop ${LANG_ARABIC} "ينشئ اختصارًا على سطح المكتب للوصول السريع إلى ${APPNAME}."
LangString DESC_SecStartMenu ${LANG_ARABIC} "ينشئ اختصارات في قائمة ابدأ تتضمن مشغل التطبيق والوثائق."
LangString DESC_SecFileAssoc ${LANG_ARABIC} "يربط أنواع ملفات TLT بالتطبيق للتعامل السلس مع الملفات."

;Assign language strings to sections
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SecMain} $(DESC_SecMain)
  !insertmacro MUI_DESCRIPTION_TEXT ${SecDesktop} $(DESC_SecDesktop)
  !insertmacro MUI_DESCRIPTION_TEXT ${SecStartMenu} $(DESC_SecStartMenu)
  !insertmacro MUI_DESCRIPTION_TEXT ${SecFileAssoc} $(DESC_SecFileAssoc)
!insertmacro MUI_FUNCTION_DESCRIPTION_END

;--------------------------------
;Uninstaller Section

Section "Uninstall"

  DetailPrint "Removing ${APPNAME}..."

  ; Stop any running instances
  DetailPrint "Stopping running instances..."
  nsExec::Exec 'taskkill /f /im "TLT Group Management System.exe"'

  ; Remove registry keys
  DetailPrint "Cleaning registry..."
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}"
  DeleteRegKey HKLM "Software\${COMPANYNAME}\${APPNAME}"
  DeleteRegKey HKCU "Software\${COMPANYNAME}\${APPNAME}"

  ; Remove file associations
  DetailPrint "Removing file associations..."
  DeleteRegKey HKCR ".tlt"
  DeleteRegKey HKCR "TLTGroupFile"
  DeleteRegKey HKCR ".tltbackup"
  DeleteRegKey HKCR "TLTGroupBackup"

  ; Refresh shell
  System::Call 'shell32.dll::SHChangeNotify(l, l, i, i) v (0x08000000, 0, 0, 0)'

  ; Remove application files
  DetailPrint "Removing application files..."
  RMDir /r "$INSTDIR\_internal"
  Delete "$INSTDIR\TLT Group Management System.exe"
  Delete "$INSTDIR\readme.txt"
  Delete "$INSTDIR\system_check.py"
  Delete "$INSTDIR\Uninstall.exe"

  ; Remove directories
  DetailPrint "Removing application directories..."
  RMDir /r "$INSTDIR\logs"
  RMDir /r "$INSTDIR\backups"
  RMDir /r "$INSTDIR\reports"
  RMDir /r "$INSTDIR\invoices"
  RMDir /r "$INSTDIR\labels"
  RMDir /r "$INSTDIR\barcodes"

  ; Remove main installation directory
  RMDir "$INSTDIR"

  ; Remove parent directory if empty
  RMDir "$PROGRAMFILES64\TLT Group"

  ; Remove shortcuts
  DetailPrint "Removing shortcuts..."
  Delete "$DESKTOP\${APPNAME}.lnk"

  ; Remove Start Menu folder
  !insertmacro MUI_STARTMENU_GETFOLDER Application $StartMenuFolder
  Delete "$SMPROGRAMS\$StartMenuFolder\${APPNAME}.lnk"
  Delete "$SMPROGRAMS\$StartMenuFolder\User Guide.lnk"
  Delete "$SMPROGRAMS\$StartMenuFolder\Uninstall ${APPNAME}.lnk"
  RMDir "$SMPROGRAMS\$StartMenuFolder"

  ; Ask user about data preservation
  MessageBox MB_YESNO|MB_ICONQUESTION \
    "Do you want to keep your data files?$\n$\nThis includes:$\n• Invoice data in Documents\TLT_Invoices$\n• Reports in Documents\TLT_Reports$\n• Backups in Documents\TLT_Backups$\n$\nChoose No to remove all data permanently." \
    IDYES preserve_data IDNO remove_data

  remove_data:
    DetailPrint "Removing user data..."
    RMDir /r "$DOCUMENTS\TLT_Invoices"
    RMDir /r "$DOCUMENTS\TLT_Reports"
    RMDir /r "$DOCUMENTS\TLT_Backups"
    Goto end_uninstall

  preserve_data:
    DetailPrint "Preserving user data..."

  end_uninstall:
    DetailPrint "Uninstallation completed"

SectionEnd

;--------------------------------
;Installer Functions

Function .onInit
  ; Check Windows version
  ${IfNot} ${AtLeastWin10}
    MessageBox MB_ICONSTOP "This application requires Windows 10 or later."
    Abort
  ${EndIf}

  ; Check if running on 64-bit system
  ${IfNot} ${RunningX64}
    MessageBox MB_ICONSTOP "This application requires a 64-bit version of Windows."
    Abort
  ${EndIf}

  ; Check if already installed
  ReadRegStr $R0 HKLM "Software\${COMPANYNAME}\${APPNAME}" "InstallLocation"
  ${If} $R0 != ""
    MessageBox MB_YESNO|MB_ICONQUESTION \
      "${APPNAME} is already installed at:$\n$\n$R0$\n$\nDo you want to continue and update the existing installation?" \
      /SD IDYES IDYES continue_install
    Abort

    continue_install:
      ; Store the existing installation path
      StrCpy $INSTDIR $R0
  ${EndIf}

  ; Check available disk space (require at least 500MB)
  ${DriveSpace} "$INSTDIR" "/D=F /S=M" $R0
  ${If} $R0 < 500
    MessageBox MB_ICONSTOP "Insufficient disk space. At least 500 MB is required."
    Abort
  ${EndIf}

  ; Set default sections
  SectionSetFlags ${SecMain} 17        ; Required + Selected
  SectionSetFlags ${SecDesktop} 1      ; Selected
  SectionSetFlags ${SecStartMenu} 1    ; Selected
  SectionSetFlags ${SecFileAssoc} 1    ; Selected
FunctionEnd

Function .onInstSuccess
  ; Write installation completion time
  ${GetTime} "" "L" $0 $1 $2 $3 $4 $5 $6
  WriteRegStr HKLM "Software\${COMPANYNAME}\${APPNAME}" "InstallDate" "$2/$1/$0"

  ; Show completion message
  MessageBox MB_YESNO|MB_ICONQUESTION \
    "Installation completed successfully!$\n$\n${APPNAME} has been installed as a complete standalone application.$\n$\nNo additional dependencies are required.$\n$\nWould you like to start ${APPNAME} now?" \
    /SD IDNO IDNO skip_run

  ; Run the application
  ExecShell "" "$INSTDIR\TLT Group Management System.exe"

  skip_run:
FunctionEnd

Function .onInstFailed
  MessageBox MB_ICONSTOP "Installation failed. Please check the installation log for details."
FunctionEnd

;--------------------------------
;Uninstaller Functions

Function un.onInit
  ; Check if user has admin rights
  UserInfo::GetAccountType
  Pop $R0
  ${If} $R0 != "admin"
    MessageBox MB_ICONSTOP "Administrator rights are required to uninstall this application."
    Abort
  ${EndIf}

  MessageBox MB_YESNO|MB_ICONQUESTION \
    "Are you sure you want to completely remove ${APPNAME} and all of its components?$\n$\nThis will remove:$\n• Application files$\n• Configuration settings$\n• Desktop and Start Menu shortcuts$\n• Registry entries$\n$\nYour data files can be preserved if you choose." \
    /SD IDYES IDYES +2
  Abort
FunctionEnd

Function un.onUninstSuccess
  MessageBox MB_ICONINFORMATION \
    "${APPNAME} has been successfully removed from your computer.$\n$\nThank you for using ${APPNAME}!$\n$\nIf you preserved your data, it remains in:$\n• Documents\TLT_Invoices$\n• Documents\TLT_Reports$\n• Documents\TLT_Backups"
FunctionEnd

Function un.onUninstFailed
  MessageBox MB_ICONSTOP "Uninstallation failed. Some files may not have been removed."
FunctionEnd
