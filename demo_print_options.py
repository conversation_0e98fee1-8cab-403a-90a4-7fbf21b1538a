#!/usr/bin/env python3
"""
Demo script to test the new print with/without deposits functionality
"""

import sys
import os
from datetime import datetime, timedelta

# Add the modules directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from database import Database

def demo_print_options():
    """Demo the new print with/without deposits functionality"""
    print("Print Options Demo")
    print("=" * 40)
    
    # Initialize database
    db = Database()
    
    try:
        # Check for existing invoices with deposits
        db.cursor.execute("SELECT id, customer_id, total_amount FROM invoices LIMIT 5")
        invoices = db.cursor.fetchall()
        
        if not invoices:
            print("No existing invoices found.")
            print("Please create some invoices first using the application.")
            return
        
        print(f"Found {len(invoices)} existing invoices")
        
        # Find an invoice with deposits
        invoice_with_deposits = None
        for invoice in invoices:
            invoice_id = invoice[0]
            deposits = db.get_invoice_deposits(invoice_id)
            if deposits:
                invoice_with_deposits = invoice
                break
        
        if not invoice_with_deposits:
            print("No invoices with deposits found.")
            print("Please add some deposits to invoices first.")
            return
        
        invoice_id = invoice_with_deposits[0]
        customer_id = invoice_with_deposits[1]
        total_amount = invoice_with_deposits[2]
        
        print(f"\nUsing Invoice #{invoice_id} for demo")
        print(f"Customer ID: {customer_id}")
        print(f"Total Amount: ${total_amount:.2f}")
        
        # Show deposit information
        deposits = db.get_invoice_deposits(invoice_id)
        total_deposits = db.get_total_deposits_for_invoice(invoice_id)
        
        print(f"\nDeposit Information:")
        print(f"Number of deposits: {len(deposits)}")
        print(f"Total deposits: ${total_deposits:.2f}")
        
        for i, deposit in enumerate(deposits, 1):
            _, amount, date, description, _ = deposit
            print(f"  Deposit #{i}: ${amount:.2f} on {date}")
            if description:
                print(f"    Note: {description}")
        
        balance_due = total_amount - total_deposits
        print(f"Balance Due: ${balance_due:.2f}")
        
        print(f"\n" + "=" * 60)
        print(f"NEW PRINT OPTIONS FEATURE")
        print(f"=" * 60)
        
        print(f"\nWhen you print Invoice #{invoice_id}, you will now see a dialog with options:")
        print(f"")
        print(f"┌─────────────────────────────────────────┐")
        print(f"│            Print Options                │")
        print(f"├─────────────────────────────────────────┤")
        print(f"│  This invoice has deposit information.  │")
        print(f"│     How would you like to print it?     │")
        print(f"│                                         │")
        print(f"│  [With Deposits] [Without Deposits]     │")
        print(f"│                           [Cancel]      │")
        print(f"└─────────────────────────────────────────┘")
        print(f"")
        
        print(f"📋 **With Deposits** - Shows:")
        print(f"   • All deposit details")
        print(f"   • Total deposits: ${total_deposits:.2f}")
        print(f"   • Balance due: ${balance_due:.2f}")
        print(f"   • Payment status")
        print(f"")
        
        print(f"📋 **Without Deposits** - Shows:")
        print(f"   • Only the invoice total: ${total_amount:.2f}")
        print(f"   • No deposit information")
        print(f"   • Clean invoice for customer")
        print(f"")
        
        print(f"🎯 **Use Cases:**")
        print(f"   • **With Deposits**: Internal records, accounting")
        print(f"   • **Without Deposits**: Customer copies, clean invoices")
        print(f"")
        
        print(f"✅ **Available in:**")
        print(f"   • Customer Management → Print Invoice")
        print(f"   • Sales Management → Print Invoice")
        print(f"")
        
        print(f"📝 **How to Test:**")
        print(f"1. Open the TLT Group Management System")
        print(f"2. Go to Customer Management")
        print(f"3. Select the customer for Invoice #{invoice_id}")
        print(f"4. Click on Invoice #{invoice_id}")
        print(f"5. Click 'Print Invoice' button")
        print(f"6. You'll see the new print options dialog!")
        print(f"7. Choose 'With Deposits' or 'Without Deposits'")
        print(f"8. Compare the generated PDFs")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in demo: {e}")
        return False

if __name__ == "__main__":
    demo_print_options()
