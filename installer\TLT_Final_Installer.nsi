;===============================================================================
; TLT Group Management System - Professional NSIS Installer
; Created for TLT Group SARL
; Version: 4.0.0 - Complete Business Management Solution
;===============================================================================

;--------------------------------
; Include Modern UI
!include "MUI2.nsh"
!include "FileFunc.nsh"
!include "LogicLib.nsh"

;--------------------------------
; General Settings
!define APPNAME "TLT Group Management System"
!define COMPANYNAME "TLT Group SARL"
!define DESCRIPTION "Complete Business Management Solution"
!define VERSION "4.0.0"
!define HELPURL "https://tltgroup.com/support"
!define UPDATEURL "https://tltgroup.com/updates"
!define ABOUTURL "https://tltgroup.com"

Name "${APPNAME}"
OutFile "TLT_Group_Management_System_Final_v${VERSION}.exe"
Unicode True
BrandingText "${COMPANYNAME} - ${DESCRIPTION}"

; Installation directory
InstallDir "$PROGRAMFILES64\TLT Group\Management System"
InstallDirRegKey HKLM "Software\${COMPANYNAME}\${APPNAME}" "InstallLocation"

; Request admin privileges
RequestExecutionLevel admin

; Compression
SetCompressor /SOLID lzma
SetCompressorDictSize 32

;--------------------------------
; Variables
Var StartMenuFolder

;--------------------------------
; Interface Settings
!define MUI_ABORTWARNING
!define MUI_ICON "..\resources\TLT icon.ico"
!define MUI_UNICON "..\resources\TLT icon.ico"

; Welcome page
!define MUI_WELCOMEPAGE_TITLE "Welcome to ${APPNAME} Setup"
!define MUI_WELCOMEPAGE_TEXT "This wizard will guide you through the installation of ${APPNAME}.$\r$\n$\r$\nThis complete business management solution includes:$\r$\n• Customer Management$\r$\n• Inventory Control$\r$\n• Invoice Generation with Deposit Tracking$\r$\n• Financial Reports$\r$\n• Supplier Management$\r$\n$\r$\nClick Next to continue."

; Finish page
!define MUI_FINISHPAGE_RUN "$INSTDIR\TLT Group Management System.exe"
!define MUI_FINISHPAGE_RUN_TEXT "Start ${APPNAME}"

;--------------------------------
; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY

; Start Menu Folder Page
!define MUI_STARTMENUPAGE_DEFAULTFOLDER "${APPNAME}"
!define MUI_STARTMENUPAGE_REGISTRY_ROOT "HKLM"
!define MUI_STARTMENUPAGE_REGISTRY_KEY "Software\${COMPANYNAME}\${APPNAME}"
!define MUI_STARTMENUPAGE_REGISTRY_VALUENAME "Start Menu Folder"
!insertmacro MUI_PAGE_STARTMENU Application $StartMenuFolder

!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; Uninstaller pages
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

;--------------------------------
; Languages
!insertmacro MUI_LANGUAGE "English"
!insertmacro MUI_LANGUAGE "French"

;--------------------------------
; Version Information
VIProductVersion "${VERSION}.0"
VIAddVersionKey /LANG=${LANG_ENGLISH} "ProductName" "${APPNAME}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "CompanyName" "${COMPANYNAME}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "LegalCopyright" "© 2025 ${COMPANYNAME}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "FileDescription" "${DESCRIPTION}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "FileVersion" "${VERSION}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "ProductVersion" "${VERSION}"

;--------------------------------
; Installer Sections

Section "!${APPNAME} (Required)" SecMain
  SectionIn RO
  
  DetailPrint "Installing ${APPNAME}..."
  
  ; Set output path
  SetOutPath $INSTDIR
  
  ; Copy the entire dist folder contents
  File /r "..\dist\TLT Group Management System\*.*"
  
  ; Copy additional files
  File /nonfatal "readme.txt"
  
  ; Create user data directories
  CreateDirectory "$DOCUMENTS\TLT_Invoices"
  CreateDirectory "$DOCUMENTS\TLT_Reports"
  CreateDirectory "$DOCUMENTS\TLT_Backups"
  
  ; Store installation info
  WriteRegStr HKLM "Software\${COMPANYNAME}\${APPNAME}" "InstallLocation" $INSTDIR
  WriteRegStr HKLM "Software\${COMPANYNAME}\${APPNAME}" "Version" "${VERSION}"
  
  ; Create uninstaller
  WriteUninstaller "$INSTDIR\Uninstall.exe"
  
  ; Add to Add/Remove Programs
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayName" "${APPNAME}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "UninstallString" "$\"$INSTDIR\Uninstall.exe$\""
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "InstallLocation" "$INSTDIR"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayIcon" "$INSTDIR\TLT Group Management System.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "Publisher" "${COMPANYNAME}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayVersion" "${VERSION}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "URLInfoAbout" "${ABOUTURL}"
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "NoModify" 1
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "NoRepair" 1
  
  ; Calculate installation size
  ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
  IntFmt $0 "0x%08X" $0
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "EstimatedSize" "$0"

SectionEnd

Section "Desktop Shortcut" SecDesktop
  CreateShortcut "$DESKTOP\${APPNAME}.lnk" "$INSTDIR\TLT Group Management System.exe" "" "$INSTDIR\TLT Group Management System.exe" 0
SectionEnd

Section "Start Menu Shortcuts" SecStartMenu
  !insertmacro MUI_STARTMENU_WRITE_BEGIN Application
    CreateDirectory "$SMPROGRAMS\$StartMenuFolder"
    CreateShortcut "$SMPROGRAMS\$StartMenuFolder\${APPNAME}.lnk" "$INSTDIR\TLT Group Management System.exe"
    CreateShortcut "$SMPROGRAMS\$StartMenuFolder\Uninstall.lnk" "$INSTDIR\Uninstall.exe"
  !insertmacro MUI_STARTMENU_WRITE_END
SectionEnd

;--------------------------------
; Descriptions
LangString DESC_SecMain ${LANG_ENGLISH} "Core application files (required). Complete standalone business management system."
LangString DESC_SecDesktop ${LANG_ENGLISH} "Creates a desktop shortcut for quick access."
LangString DESC_SecStartMenu ${LANG_ENGLISH} "Creates Start Menu shortcuts."

LangString DESC_SecMain ${LANG_FRENCH} "Fichiers principaux de l'application (requis). Système de gestion d'entreprise autonome complet."
LangString DESC_SecDesktop ${LANG_FRENCH} "Crée un raccourci sur le bureau pour un accès rapide."
LangString DESC_SecStartMenu ${LANG_FRENCH} "Crée des raccourcis dans le menu Démarrer."

!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SecMain} $(DESC_SecMain)
  !insertmacro MUI_DESCRIPTION_TEXT ${SecDesktop} $(DESC_SecDesktop)
  !insertmacro MUI_DESCRIPTION_TEXT ${SecStartMenu} $(DESC_SecStartMenu)
!insertmacro MUI_FUNCTION_DESCRIPTION_END

;--------------------------------
; Uninstaller Section
Section "Uninstall"
  ; Stop running instances
  nsExec::Exec 'taskkill /f /im "TLT Group Management System.exe"'
  
  ; Remove files
  RMDir /r "$INSTDIR\_internal"
  Delete "$INSTDIR\TLT Group Management System.exe"
  Delete "$INSTDIR\readme.txt"
  Delete "$INSTDIR\Uninstall.exe"
  RMDir "$INSTDIR"
  RMDir "$PROGRAMFILES64\TLT Group"
  
  ; Remove shortcuts
  Delete "$DESKTOP\${APPNAME}.lnk"
  !insertmacro MUI_STARTMENU_GETFOLDER Application $StartMenuFolder
  Delete "$SMPROGRAMS\$StartMenuFolder\${APPNAME}.lnk"
  Delete "$SMPROGRAMS\$StartMenuFolder\Uninstall.lnk"
  RMDir "$SMPROGRAMS\$StartMenuFolder"
  
  ; Remove registry keys
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}"
  DeleteRegKey HKLM "Software\${COMPANYNAME}\${APPNAME}"
  
  ; Ask about user data
  MessageBox MB_YESNO "Do you want to keep your data files in Documents?" IDYES preserve_data
  RMDir /r "$DOCUMENTS\TLT_Invoices"
  RMDir /r "$DOCUMENTS\TLT_Reports"
  RMDir /r "$DOCUMENTS\TLT_Backups"
  
  preserve_data:
SectionEnd

;--------------------------------
; Functions
Function .onInit
  ; Set default sections
  SectionSetFlags ${SecMain} 17        ; Required + Selected
  SectionSetFlags ${SecDesktop} 1      ; Selected
  SectionSetFlags ${SecStartMenu} 1    ; Selected
FunctionEnd

Function .onInstSuccess
  MessageBox MB_YESNO "Installation completed successfully!$\n$\nWould you like to start ${APPNAME} now?" IDNO skip_run
  ExecShell "" "$INSTDIR\TLT Group Management System.exe"
  skip_run:
FunctionEnd

Function un.onInit
  MessageBox MB_YESNO "Are you sure you want to remove ${APPNAME}?" IDYES +2
  Abort
FunctionEnd
