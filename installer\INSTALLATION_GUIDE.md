# TLT Group Management System - Installation Guide

## Overview
This guide will help you create and distribute an installer for the TLT Group Management System.

## Prerequisites

### For Windows Installer (Recommended)
1. **Inno Setup 6** - Download from: https://jrsoftware.org/isinfo.php
2. **Python 3.8+** - For testing the application

### For MSYS2 Environment
```bash
# Install Inno Setup via MSYS2
pacman -S mingw-w64-x86_64-innosetup
```

## Building the Installer

### Method 1: Using Inno Setup (Recommended)

1. **Install Inno Setup 6** from the official website
2. **Navigate to the installer directory**:
   ```cmd
   cd installer
   ```
3. **Run the build script**:
   ```cmd
   build_installer.bat
   ```
4. **Find your installer** in the `output` folder:
   - `TLT_Group_Management_System_Setup.exe`

### Method 2: Using MSYS2

1. **Open MSYS2 terminal**
2. **Navigate to the installer directory**:
   ```bash
   cd installer
   ```
3. **Make the script executable and run**:
   ```bash
   chmod +x build_msys2.sh
   ./build_msys2.sh
   ```

### Method 3: Python-based Installer

1. **Install PyInstaller** (optional, for executable):
   ```cmd
   pip install pyinstaller
   ```
2. **Run the Python installer creator**:
   ```cmd
   python create_installer.py
   ```

## What the Installer Includes

### Application Files
- `main.py` - Main application entry point
- `modules/` - All Python modules
- `resources/` - Icons, images, and other resources
- `config/` - Configuration files
- `requirements.txt` - Python dependencies

### Installer Components
- `run_app.bat` - Batch file to launch the application
- `license.txt` - Software license agreement
- `readme.txt` - Installation and usage instructions

## Installation Process

### For End Users
1. **Download** the installer (`TLT_Group_Management_System_Setup.exe`)
2. **Run as Administrator** (right-click → "Run as administrator")
3. **Follow the installation wizard**:
   - Accept the license agreement
   - Choose installation directory
   - Select additional tasks (desktop shortcut, etc.)
4. **Launch the application** from:
   - Desktop shortcut
   - Start Menu → TLT Group Management System

### Automatic Features
- **Python Detection**: Installer checks if Python is installed
- **Dependency Installation**: Automatically installs required Python packages
- **Shortcut Creation**: Creates desktop and Start Menu shortcuts
- **Uninstaller**: Provides clean uninstallation option

## System Requirements

### Minimum Requirements
- **OS**: Windows 10 (64-bit) or later
- **RAM**: 4 GB
- **Storage**: 500 MB free space
- **Python**: 3.8+ (installed automatically if needed)

### Recommended Requirements
- **OS**: Windows 11 (64-bit)
- **RAM**: 8 GB
- **Storage**: 1 GB free space
- **Internet**: For initial setup and updates

## Troubleshooting

### Build Issues

**"Inno Setup not found"**
- Install Inno Setup 6 from the official website
- Ensure it's installed in the default location
- For MSYS2: `pacman -S mingw-w64-x86_64-innosetup`

**"Python not found"**
- Install Python 3.8+ from python.org
- Ensure Python is added to PATH during installation

### Installation Issues

**"Permission denied"**
- Run installer as Administrator
- Ensure antivirus is not blocking the installation

**"Python packages failed to install"**
- Check internet connection
- Run: `pip install -r requirements.txt` manually

## Distribution

### File Sizes (Approximate)
- **Installer**: 15-25 MB
- **Installed Application**: 50-100 MB
- **With Python Runtime**: 100-200 MB

### Distribution Methods
1. **Direct Download**: Host the installer on your website
2. **USB/CD**: Copy installer to removable media
3. **Network Share**: Place on company network drive
4. **Email**: Send as attachment (check size limits)

## Security Notes

### Code Signing (Recommended)
For production distribution, consider code signing the installer:
1. Obtain a code signing certificate
2. Use `signtool.exe` to sign the installer
3. This prevents Windows security warnings

### Antivirus Considerations
- Some antivirus software may flag unsigned executables
- Consider submitting to antivirus vendors for whitelisting
- Provide SHA256 checksums for verification

## Support

### For Installation Issues
- Check the `readme.txt` file included with the installer
- Verify system requirements
- Contact: <EMAIL>

### For Development Issues
- Check Python and package versions
- Verify all dependencies are included
- Test on clean Windows installation

## Version Management

### Updating the Installer
1. Update the version number in `setup.iss`
2. Update the changelog in `readme.txt`
3. Rebuild the installer
4. Test on clean system

### Automatic Updates
Consider implementing an auto-update mechanism:
- Check for updates on application startup
- Download and install updates automatically
- Notify users of available updates

---

**TLT Group SARL**  
© 2025 All Rights Reserved
