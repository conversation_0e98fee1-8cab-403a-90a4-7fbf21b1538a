# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_all

datas = [('C:\\Users\\<USER>\\Desktop\\music project\\Accounting101\\resources', 'resources'), ('C:\\Users\\<USER>\\Desktop\\music project\\Accounting101\\config', 'config')]
binaries = []
hiddenimports = ['reportlab.pdfgen.canvas', 'reportlab.lib.pagesizes', 'reportlab.platypus', 'PIL.Image', 'PIL.ImageTk', 'tkinter.ttk', 'sqlite3']
tmp_ret = collect_all('reportlab')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
tmp_ret = collect_all('PIL')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]


a = Analysis(
    ['C:\\Users\\<USER>\\Desktop\\music project\\Accounting101\\main.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='TLT Group Management System',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['C:\\Users\\<USER>\\Desktop\\music project\\Accounting101\\resources\\TLT icon.ico'],
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='TLT Group Management System',
)
