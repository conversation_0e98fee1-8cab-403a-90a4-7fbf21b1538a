@echo off
title Building TLT Group Management System Installer
echo ========================================
echo Building TLT Group Management System Installer
echo ========================================
echo.

REM Check if Inno Setup is installed
set "INNO_SETUP_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
if not exist "%INNO_SETUP_PATH%" (
    echo Inno Setup 6 not found at: %INNO_SETUP_PATH%
    echo.
    echo Please install Inno Setup 6 from: https://jrsoftware.org/isinfo.php
    echo.
    echo Alternative paths to check:
    echo - C:\Program Files\Inno Setup 6\ISCC.exe
    echo - C:\msys64\mingw64\bin\iscc.exe
    echo.
    pause
    exit /b 1
)

REM Create output directory if it doesn't exist
if not exist "output" mkdir output

REM Clean previous builds
if exist "output\*.exe" del /q "output\*.exe"

echo Compiling installer...
echo.

REM Compile the installer
"%INNO_SETUP_PATH%" "setup.iss"

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo Installer built successfully!
    echo ========================================
    echo.
    echo Output file: output\TLT_Group_Management_System_Setup.exe
    echo.
    echo You can now distribute this installer to install the
    echo TLT Group Management System on other computers.
    echo.
    
    REM Open output folder
    explorer "output"
) else (
    echo.
    echo ========================================
    echo Build failed!
    echo ========================================
    echo.
    echo Please check the error messages above and fix any issues.
    echo.
)

pause
