2025-06-08 22:25:59,340 - root - INFO - ===== TLT Group Management System Starting =====
2025-06-08 22:25:59,341 - root - INFO - Log file created at: C:\Users\<USER>\Desktop\music project\Accounting101\logs\application_20250608_222559.log
2025-06-08 22:25:59,400 - root - INFO - Searching for icon in: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-08 22:25:59,461 - root - INFO - Set icon using ICO file: C:\Users\<USER>\Desktop\music project\Accounting101\resources\TLT icon.ico
2025-06-08 22:25:59,507 - root - INFO - Found existing database at: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-08 22:25:59,508 - root - INFO - Selected database path: C:\Users\<USER>\Desktop\music project\Accounting101\accounting.db
2025-06-08 22:25:59,508 - root - INFO - Ensured database directory exists: C:\Users\<USER>\Desktop\music project\Accounting101
2025-06-08 22:25:59,524 - root - INFO - Database initialized successfully with WAL journal mode
2025-06-08 22:25:59,527 - root - INFO - Database check completed successfully
2025-06-08 22:25:59,530 - root - INFO - Loading module: Sales
2025-06-08 22:25:59,628 - root - INFO - Successfully loaded module: Sales
2025-06-08 22:25:59,628 - root - INFO - Loading module: Customers
2025-06-08 22:25:59,709 - root - INFO - Successfully loaded module: Customers
2025-06-08 22:25:59,710 - root - INFO - Loading module: Suppliers
2025-06-08 22:25:59,721 - modules.utils.table_customizer - INFO - Applied column order for suppliers: ['name', 'contact', 'address', 'balance']
2025-06-08 22:25:59,721 - modules.utils.table_customizer - INFO - Loaded saved column order for suppliers
2025-06-08 22:25:59,748 - root - INFO - Successfully loaded module: Suppliers
2025-06-08 22:25:59,749 - root - INFO - Loading module: Purchases
2025-06-08 22:25:59,794 - root - INFO - Successfully loaded module: Purchases
2025-06-08 22:25:59,794 - root - INFO - Loading module: Inventory
2025-06-08 22:25:59,846 - modules.utils.barcode_generator_new - INFO - Successfully imported PIL modules
2025-06-08 22:25:59,883 - modules.utils.barcode_generator_new - INFO - Successfully imported and tested barcode module
2025-06-08 22:26:00,039 - modules.utils.label_generator - INFO - Successfully imported reportlab modules
2025-06-08 22:26:00,101 - modules.utils.table_customizer - INFO - Applied column order for inventory: ['barcode', 'name', 'quantity', 'price', 'cost price', 'category', 'min stock', 'description']
2025-06-08 22:26:00,101 - modules.utils.table_customizer - INFO - Loaded saved column order for inventory
2025-06-08 22:26:00,104 - root - INFO - Successfully loaded module: Inventory
2025-06-08 22:26:00,105 - root - INFO - Loading module: Reports
2025-06-08 22:26:00,141 - root - INFO - Successfully loaded module: Reports
2025-06-08 22:26:00,142 - root - INFO - Loading module: Settings
2025-06-08 22:26:00,240 - root - INFO - Successfully loaded module: Settings
2025-06-08 22:26:00,240 - root - INFO - Application starting in unlocked state
2025-06-08 22:26:00,240 - root - INFO - Initializing security timer
2025-06-08 22:26:00,241 - root - INFO - Loaded security delay from config: 0 seconds
2025-06-08 22:26:00,241 - root - INFO - SecurityTimer initialized with delay: 0 seconds
2025-06-08 22:26:00,241 - root - INFO - Security timer initialized successfully
2025-06-08 22:26:00,241 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-08 22:26:03,448 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-08 22:26:03,448 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
2025-06-08 22:26:06,396 - root - INFO - Changed to tab: Inventory (index: 4)
2025-06-08 22:26:06,397 - root - INFO - Security delay is set to immediate, requiring password for Inventory tab
2025-06-08 22:26:37,018 - root - INFO - Changed to tab: Sales (index: 0)
2025-06-08 22:27:01,510 - root - INFO - Changed to tab: Customers (index: 1)
2025-06-08 22:27:01,510 - root - INFO - Security delay is set to immediate, requiring password for Customers tab
2025-06-08 22:29:07,819 - root - INFO - Application closing - performing cleanup
2025-06-08 22:29:07,867 - root - INFO - ===== TLT Group Management System Shutting Down =====
